<?php
/**
 * Airtable Integration for RepairLift WP Customizer
 * 
 * Handles webhook reception and content population from Airtable forms
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class AirtableIntegration {
    
    private $webhook_handler;
    private $field_mapper;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->init();
    }
    
    /**
     * Initialize the integration
     */
    public function init() {
        // Load dependencies
        require_once plugin_dir_path(__FILE__) . 'airtable-webhook-handler.php';
        require_once plugin_dir_path(__FILE__) . 'airtable-field-mapper.php';
        
        $this->webhook_handler = new AirtableWebhookHandler();
        $this->field_mapper = new AirtableFieldMapper();
        
        // Register hooks
        add_action('init', array($this, 'register_webhook_endpoint'));
        add_action('wp_ajax_nopriv_airtable_webhook', array($this, 'handle_webhook'));
        add_action('wp_ajax_airtable_webhook', array($this, 'handle_webhook'));
        
        // Admin hooks
        add_action('wp_ajax_test_airtable_webhook', array($this, 'test_webhook_connection'));
        add_action('wp_ajax_save_airtable_config', array($this, 'save_airtable_config'));
        add_action('wp_ajax_get_airtable_logs', array($this, 'get_webhook_logs'));
        
        // Add admin menu
        add_action('admin_menu', array($this, 'add_airtable_admin_menu'));
    }
    
    /**
     * Register webhook endpoint
     */
    public function register_webhook_endpoint() {
        add_rewrite_rule(
            '^airtable-webhook/?$',
            'index.php?airtable_webhook=1',
            'top'
        );
        add_rewrite_tag('%airtable_webhook%', '([^&]+)');
    }
    
    /**
     * Handle incoming webhook from Airtable
     */
    public function handle_webhook() {
        try {
            // Log detailed request information for debugging
            $this->log_detailed_request();

            // Verify request method
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                $this->log_error('Invalid request method: ' . $_SERVER['REQUEST_METHOD']);
                wp_die('Method not allowed', 'Method Not Allowed', array('response' => 405));
            }

            // Get raw POST data
            $raw_data = file_get_contents('php://input');
            if (empty($raw_data)) {
                $this->log_error('No POST data received');
                wp_die('No data received', 'Bad Request', array('response' => 400));
            }

            $this->log_success('Raw data received', array('length' => strlen($raw_data), 'data' => substr($raw_data, 0, 500)));

            // Decode JSON data
            $data = json_decode($raw_data, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                $this->log_error('JSON decode error: ' . json_last_error_msg() . ' | Raw data: ' . $raw_data);
                wp_die('Invalid JSON data', 'Bad Request', array('response' => 400));
            }

            $this->log_success('JSON decoded successfully', $data);

            // Log the webhook attempt
            $this->log_webhook_attempt($data);

            // Detailed validation logging
            $validation_result = $this->detailed_webhook_validation($data, $_SERVER);
            if (!$validation_result['valid']) {
                $this->log_error('Webhook validation failed: ' . $validation_result['reason'], $validation_result['details']);
                wp_die('Unauthorized', 'Unauthorized', array('response' => 401));
            }

            $this->log_success('Webhook validation passed');

            // Process the webhook data
            $result = $this->process_webhook_data($data);

            if ($result['success']) {
                $this->log_success('Webhook processed successfully', $result);
                wp_send_json_success($result);
            } else {
                $this->log_error('Webhook processing failed', $result);
                wp_send_json_error($result);
            }

        } catch (Exception $e) {
            $this->log_error('Webhook processing exception: ' . $e->getMessage() . ' | Trace: ' . $e->getTraceAsString());
            wp_die('Internal server error', 'Internal Server Error', array('response' => 500));
        }
    }
    
    /**
     * Process webhook data and update WordPress content
     */
    private function process_webhook_data($data) {
        $backup_id = null;

        try {
            // Create backup before making changes
            $backup_id = $this->create_pre_webhook_backup();
            if (!$backup_id) {
                return array(
                    'success' => false,
                    'message' => 'Failed to create backup before processing'
                );
            }

            // Map Airtable fields to WordPress fields
            $mapped_data = $this->field_mapper->map_airtable_to_wordpress($data);

            // DETAILED LOGGING: Log received data and mapped data
            $this->log_success('WEBHOOK DATA RECEIVED', array(
                'raw_data' => $data,
                'raw_data_keys' => array_keys($data),
                'mapped_data' => $mapped_data,
                'mapped_sections' => array_keys($mapped_data),
                'business_info_count' => count($mapped_data['business_info'] ?? array()),
                'visual_blocks_count' => count($mapped_data['visual_blocks'] ?? array()),
                'design_system_count' => count($mapped_data['design_system'] ?? array()),
                'visual_blocks_fields' => array_keys($mapped_data['visual_blocks'] ?? array()),
                'hero_heading_value' => $mapped_data['visual_blocks']['hero_heading'] ?? 'NOT_FOUND'
            ));

            // Validate mapped data
            $validation_result = $this->validate_mapped_data($mapped_data);
            if (!$validation_result['valid']) {
                return array(
                    'success' => false,
                    'message' => 'Data validation failed: ' . $validation_result['errors'],
                    'backup_id' => $backup_id
                );
            }

            // Track which updates succeed for rollback purposes
            $completed_updates = array();

            // Update business information
            if (!empty($mapped_data['business_info'])) {
                $business_result = $this->update_business_info($mapped_data['business_info']);
                if (!$business_result) {
                    $this->restore_from_backup($backup_id);
                    return array(
                        'success' => false,
                        'message' => 'Failed to update business information - changes rolled back',
                        'backup_id' => $backup_id
                    );
                }
                $completed_updates[] = 'business_info';
            }

            // Update visual blocks content
            if (!empty($mapped_data['visual_blocks'])) {
                $content_result = $this->update_visual_content($mapped_data['visual_blocks']);
                if (!$content_result) {
                    $this->restore_from_backup($backup_id);
                    return array(
                        'success' => false,
                        'message' => 'Failed to update visual content - changes rolled back',
                        'backup_id' => $backup_id,
                        'completed_updates' => $completed_updates
                    );
                }
                $completed_updates[] = 'visual_blocks';
            }

            // Update design system
            if (!empty($mapped_data['design_system'])) {
                $design_result = $this->update_design_system($mapped_data['design_system']);
                if (!$design_result) {
                    $this->restore_from_backup($backup_id);
                    return array(
                        'success' => false,
                        'message' => 'Failed to update design system - changes rolled back',
                        'backup_id' => $backup_id,
                        'completed_updates' => $completed_updates
                    );
                }
                $completed_updates[] = 'design_system';
            }

            // Handle logo upload
            if (!empty($mapped_data['logo'])) {
                $logo_result = $this->process_logo_upload($mapped_data['logo']);
                if (!$logo_result) {
                    // Logo upload failure is not critical - don't rollback other changes
                    $this->log_error('Logo upload failed but other changes preserved');
                    $completed_updates[] = 'logo_failed';
                } else {
                    $completed_updates[] = 'logo';
                }
            }

            // Log successful processing
            $this->log_success('Webhook processed successfully', array(
                'data_summary' => array(
                    'business_info_fields' => count($mapped_data['business_info'] ?? array()),
                    'visual_blocks_fields' => count($mapped_data['visual_blocks'] ?? array()),
                    'design_system_fields' => count($mapped_data['design_system'] ?? array()),
                    'logo_included' => !empty($mapped_data['logo'])
                ),
                'completed_updates' => $completed_updates
            ));

            return array(
                'success' => true,
                'message' => 'Content updated successfully',
                'backup_id' => $backup_id,
                'updated_sections' => $completed_updates,
                'total_fields_updated' => array_sum(array(
                    count($mapped_data['business_info'] ?? array()),
                    count($mapped_data['visual_blocks'] ?? array()),
                    count($mapped_data['design_system'] ?? array())
                ))
            );

        } catch (Exception $e) {
            // Attempt to restore from backup on any exception
            if ($backup_id) {
                $this->restore_from_backup($backup_id);
                $this->log_error('Processing error - restored from backup: ' . $e->getMessage());
            } else {
                $this->log_error('Processing error - no backup available: ' . $e->getMessage());
            }

            return array(
                'success' => false,
                'message' => 'Processing error: ' . $e->getMessage(),
                'backup_id' => $backup_id,
                'restored' => !empty($backup_id)
            );
        }
    }
    
    /**
     * Update business information
     */
    private function update_business_info($business_data) {
        try {
            $business_info_collector = new BusinessInfoCollector();
            
            // Get current business info
            $current_info = get_option('website_generator_business_info', array());
            
            // Merge with new data
            $updated_info = array_merge($current_info, $business_data);
            
            // Save updated business info
            update_option('website_generator_business_info', $updated_info);
            
            return true;
        } catch (Exception $e) {
            $this->log_error('Business info update error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update visual blocks content
     */
    private function update_visual_content($visual_data) {
        try {
            // Get current custom texts
            $custom_texts = get_option('website_generator_custom_texts', array());

            // DETAILED LOGGING: Log visual content update process
            $this->log_success('VISUAL CONTENT UPDATE PROCESS', array(
                'incoming_visual_data' => $visual_data,
                'incoming_data_keys' => array_keys($visual_data),
                'current_custom_texts' => $custom_texts,
                'current_texts_keys' => array_keys($custom_texts),
                'hero_heading_incoming' => $visual_data['hero_heading'] ?? 'NOT_PROVIDED',
                'hero_heading_current' => $custom_texts['hero_heading'] ?? 'NOT_SET'
            ));

            // Use proper TextManager system instead of ContentBlockManager hack
            // ContentBlockManager disabled - TextManager handles frontend text replacement

            // Merge with new data
            $updated_texts = array_merge($custom_texts, $visual_data);

            // DETAILED LOGGING: Log the merged result
            $this->log_success('VISUAL CONTENT MERGED RESULT', array(
                'updated_texts' => $updated_texts,
                'updated_texts_keys' => array_keys($updated_texts),
                'hero_heading_final' => $updated_texts['hero_heading'] ?? 'STILL_NOT_SET'
            ));

            // Save updated texts
            update_option('website_generator_custom_texts', $updated_texts);

            // Text replacement handled by TextManager on frontend - no block updates needed
            $block_updates = array(
                'status' => 'TextManager handles frontend replacement',
                'method' => 'JavaScript text replacement on page load'
            );

            // DETAILED LOGGING: Verify the save worked
            $saved_texts = get_option('website_generator_custom_texts', array());
            $this->log_success('VISUAL CONTENT SAVE VERIFICATION', array(
                'save_successful' => !empty($saved_texts),
                'saved_hero_heading' => $saved_texts['hero_heading'] ?? 'SAVE_FAILED',
                'saved_texts_count' => count($saved_texts),
                'all_saved_texts' => $saved_texts,
                'block_updates' => $block_updates
            ));

            // ALSO LOG TO ERROR LOG FOR EASY DEBUGGING
            error_log('AIRTABLE WEBHOOK: Hero heading saved as: ' . ($saved_texts['hero_heading'] ?? 'NOT_SAVED'));
            error_log('AIRTABLE WEBHOOK: Block updates: ' . print_r($block_updates, true));

            return true;
        } catch (Exception $e) {
            $this->log_error('Visual content update error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update design system
     */
    private function update_design_system($design_data) {
        try {
            // Get current design settings
            $design_settings = get_option('website_generator_design_settings', array());
            
            // Merge with new data
            $updated_settings = array_merge($design_settings, $design_data);
            
            // Save updated settings
            update_option('website_generator_design_settings', $updated_settings);
            
            return true;
        } catch (Exception $e) {
            $this->log_error('Design system update error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Process logo upload from Airtable
     */
    private function process_logo_upload($logo_data) {
        try {
            // This will be implemented to handle logo downloads from Airtable
            // and upload to WordPress media library
            return true;
        } catch (Exception $e) {
            $this->log_error('Logo upload error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Create backup before webhook processing
     */
    private function create_pre_webhook_backup() {
        try {
            // Create comprehensive backup with Airtable context
            $backup_data = array(
                'type' => 'airtable_webhook',
                'description' => 'Automatic backup before Airtable form submission processing',
                'timestamp' => current_time('mysql'),
                'source' => 'airtable_integration'
            );

            // Get current state for backup
            $current_state = array(
                'business_info' => get_option('website_generator_business_info', array()),
                'custom_texts' => get_option('website_generator_custom_texts', array()),
                'design_settings' => get_option('website_generator_design_settings', array()),
                'ai_config' => array(
                    'claude_api_key' => get_option('website_generator_claude_api_key', ''),
                    'claude_model' => get_option('website_generator_claude_model', ''),
                    'claude_temperature' => get_option('website_generator_claude_temperature', 0.7),
                    'claude_max_tokens' => get_option('website_generator_claude_max_tokens', 1000)
                )
            );

            // Create backup using WordPress options
            $backup_id = 'airtable_' . time() . '_' . wp_generate_password(8, false);
            $backup_option_key = 'website_generator_backup_' . $backup_id;

            $backup_content = array(
                'id' => $backup_id,
                'metadata' => $backup_data,
                'content' => $current_state,
                'created_at' => current_time('mysql'),
                'created_by' => 'airtable_integration'
            );

            // Save backup
            $saved = update_option($backup_option_key, $backup_content);

            if ($saved) {
                // Add to backup index
                $backup_index = get_option('website_generator_backup_index', array());
                $backup_index[$backup_id] = array(
                    'id' => $backup_id,
                    'type' => 'airtable_webhook',
                    'description' => $backup_data['description'],
                    'created_at' => $backup_data['timestamp'],
                    'size' => strlen(serialize($backup_content))
                );
                update_option('website_generator_backup_index', $backup_index);

                $this->log_success('Backup created successfully', array('backup_id' => $backup_id));
                return $backup_id;
            } else {
                $this->log_error('Failed to save backup to database');
                return false;
            }

        } catch (Exception $e) {
            $this->log_error('Backup creation error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Restore from backup if webhook processing fails
     */
    private function restore_from_backup($backup_id) {
        try {
            if (empty($backup_id)) {
                return false;
            }

            $backup_option_key = 'website_generator_backup_' . $backup_id;
            $backup_content = get_option($backup_option_key);

            if (!$backup_content || !isset($backup_content['content'])) {
                $this->log_error('Backup not found or invalid: ' . $backup_id);
                return false;
            }

            $content = $backup_content['content'];

            // Restore business info
            if (isset($content['business_info'])) {
                update_option('website_generator_business_info', $content['business_info']);
            }

            // Restore custom texts
            if (isset($content['custom_texts'])) {
                update_option('website_generator_custom_texts', $content['custom_texts']);
            }

            // Restore design settings
            if (isset($content['design_settings'])) {
                update_option('website_generator_design_settings', $content['design_settings']);
            }

            $this->log_success('Restored from backup successfully', array('backup_id' => $backup_id));
            return true;

        } catch (Exception $e) {
            $this->log_error('Backup restoration error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Clean up old Airtable backups
     */
    public function cleanup_old_backups() {
        try {
            $backup_index = get_option('website_generator_backup_index', array());
            $retention_days = get_option('airtable_backup_retention_days', 30);
            $cutoff_time = strtotime("-{$retention_days} days");

            $deleted_count = 0;

            foreach ($backup_index as $backup_id => $backup_info) {
                if (isset($backup_info['type']) && $backup_info['type'] === 'airtable_webhook') {
                    $backup_time = strtotime($backup_info['created_at']);

                    if ($backup_time < $cutoff_time) {
                        // Delete backup
                        $backup_option_key = 'website_generator_backup_' . $backup_id;
                        delete_option($backup_option_key);

                        // Remove from index
                        unset($backup_index[$backup_id]);
                        $deleted_count++;
                    }
                }
            }

            if ($deleted_count > 0) {
                update_option('website_generator_backup_index', $backup_index);
                $this->log_success("Cleaned up {$deleted_count} old Airtable backups");
            }

            return $deleted_count;

        } catch (Exception $e) {
            $this->log_error('Backup cleanup error: ' . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Validate mapped data
     */
    private function validate_mapped_data($data) {
        $errors = array();
        
        // For partial updates, we don't require business name
        // Only validate if we're doing a full site generation
        $has_business_info = !empty($data['business_info']);
        $has_multiple_sections = count($data) > 1;

        // Only require business name for full site updates
        if ($has_multiple_sections && $has_business_info && empty($data['business_info']['business_name'])) {
            $errors[] = 'Business name is required for full site updates';
        }

        // Log validation details for debugging
        $this->log_success('Data validation details', array(
            'has_business_info' => $has_business_info,
            'has_multiple_sections' => $has_multiple_sections,
            'data_sections' => array_keys($data),
            'validation_errors' => $errors
        ));
        
        // Validate color codes
        if (!empty($data['design_system'])) {
            foreach ($data['design_system'] as $key => $value) {
                if (strpos($key, 'color') !== false && !preg_match('/^#[0-9A-Fa-f]{6}$/', $value)) {
                    $errors[] = "Invalid color code for {$key}: {$value}";
                }
            }
        }
        
        return array(
            'valid' => empty($errors),
            'errors' => implode(', ', $errors)
        );
    }

    /**
     * Log detailed request information for debugging
     */
    private function log_detailed_request() {
        $request_info = array(
            'method' => $_SERVER['REQUEST_METHOD'] ?? 'unknown',
            'content_type' => $_SERVER['CONTENT_TYPE'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'headers' => array(),
            'airtable_config' => array(
                'integration_enabled' => get_option('airtable_integration_enabled', false),
                'api_key_configured' => !empty(get_option('airtable_api_key', '')),
                'webhook_secret_configured' => !empty(get_option('airtable_webhook_secret', '')),
                'site_identifier' => get_option('airtable_site_identifier', ''),
            )
        );

        // Capture relevant headers
        foreach ($_SERVER as $key => $value) {
            if (strpos($key, 'HTTP_') === 0) {
                $header_name = str_replace('HTTP_', '', $key);
                $request_info['headers'][$header_name] = $value;
            }
        }

        $this->log_success('Detailed request info', $request_info);
    }

    /**
     * Detailed webhook validation with step-by-step logging
     */
    private function detailed_webhook_validation($data, $server_data) {
        $result = array('valid' => false, 'reason' => '', 'details' => array());

        // Step 1: Check if integration is enabled
        $integration_enabled = get_option('airtable_integration_enabled', false);
        $result['details']['integration_enabled'] = $integration_enabled;

        if (!$integration_enabled) {
            $result['reason'] = 'Airtable integration is disabled';
            return $result;
        }

        // Step 2: Check API key validation
        $api_key_result = $this->detailed_api_key_validation($data);
        $result['details']['api_key_validation'] = $api_key_result;

        if (!$api_key_result['valid']) {
            $result['reason'] = 'API key validation failed: ' . $api_key_result['reason'];
            return $result;
        }

        // Step 3: Check webhook signature (if configured)
        $webhook_secret = get_option('airtable_webhook_secret', '');
        if (!empty($webhook_secret)) {
            $signature_result = $this->detailed_signature_validation($data, $server_data);
            $result['details']['signature_validation'] = $signature_result;

            if (!$signature_result['valid']) {
                $result['reason'] = 'Signature validation failed: ' . $signature_result['reason'];
                return $result;
            }
        } else {
            $result['details']['signature_validation'] = array('skipped' => 'No webhook secret configured');
        }

        // Step 4: Check site identifier
        $site_id_result = $this->detailed_site_identifier_validation($data);
        $result['details']['site_identifier_validation'] = $site_id_result;

        if (!$site_id_result['valid']) {
            $result['reason'] = 'Site identifier validation failed: ' . $site_id_result['reason'];
            return $result;
        }

        // Step 5: Check rate limiting
        $rate_limit_result = $this->detailed_rate_limit_check();
        $result['details']['rate_limit_check'] = $rate_limit_result;

        if (!$rate_limit_result['valid']) {
            $result['reason'] = 'Rate limit exceeded: ' . $rate_limit_result['reason'];
            return $result;
        }

        $result['valid'] = true;
        return $result;
    }

    /**
     * Detailed API key validation
     */
    private function detailed_api_key_validation($data) {
        $result = array('valid' => false, 'reason' => '', 'details' => array());

        $configured_api_key = get_option('airtable_api_key', '');
        $result['details']['configured_api_key_length'] = strlen($configured_api_key);
        $result['details']['configured_api_key_empty'] = empty($configured_api_key);

        if (empty($configured_api_key)) {
            $result['reason'] = 'No API key configured in WordPress';
            return $result;
        }

        // Check for API key in various locations
        $provided_key = '';
        $key_source = '';

        if (isset($data['api_key'])) {
            $provided_key = $data['api_key'];
            $key_source = 'request_body';
        } elseif (isset($_SERVER['HTTP_X_API_KEY'])) {
            $provided_key = $_SERVER['HTTP_X_API_KEY'];
            $key_source = 'X-API-Key header';
        } elseif (isset($_SERVER['HTTP_AUTHORIZATION'])) {
            $auth_header = $_SERVER['HTTP_AUTHORIZATION'];
            if (strpos($auth_header, 'Bearer ') === 0) {
                $provided_key = substr($auth_header, 7);
                $key_source = 'Authorization Bearer header';
            }
        }

        $result['details']['provided_key_length'] = strlen($provided_key);
        $result['details']['provided_key_empty'] = empty($provided_key);
        $result['details']['key_source'] = $key_source;
        $result['details']['provided_key_preview'] = substr($provided_key, 0, 10) . '...';
        $result['details']['configured_key_preview'] = substr($configured_api_key, 0, 10) . '...';

        if (empty($provided_key)) {
            $result['reason'] = 'No API key provided in request';
            return $result;
        }

        $is_valid = hash_equals($configured_api_key, $provided_key);
        $result['details']['keys_match'] = $is_valid;

        if (!$is_valid) {
            $result['reason'] = 'API key mismatch';
            return $result;
        }

        $result['valid'] = true;
        return $result;
    }

    /**
     * Log webhook attempt
     */
    private function log_webhook_attempt($data) {
        $log_entry = array(
            'timestamp' => current_time('mysql'),
            'type' => 'webhook_attempt',
            'data' => $data,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
        );
        
        $logs = get_option('airtable_webhook_logs', array());
        $logs[] = $log_entry;
        
        // Keep only last 100 log entries
        if (count($logs) > 100) {
            $logs = array_slice($logs, -100);
        }
        
        update_option('airtable_webhook_logs', $logs);
    }
    
    /**
     * Detailed site identifier validation
     */
    private function detailed_site_identifier_validation($data) {
        $result = array('valid' => false, 'reason' => '', 'details' => array());

        $configured_identifier = get_option('airtable_site_identifier', '');
        $result['details']['configured_identifier'] = $configured_identifier;
        $result['details']['configured_identifier_empty'] = empty($configured_identifier);
        $result['details']['site_identifier_in_data'] = isset($data['site_identifier']);

        if (isset($data['site_identifier'])) {
            $result['details']['provided_identifier'] = $data['site_identifier'];
        }

        // If no identifier is configured, accept any (or none)
        if (empty($configured_identifier)) {
            $result['valid'] = true;
            $result['details']['validation_result'] = 'No site identifier configured - accepting request';
            return $result;
        }

        // If identifier is configured, it must be provided and match
        if (!isset($data['site_identifier'])) {
            $result['reason'] = 'Site identifier required but not provided';
            return $result;
        }

        $provided_identifier = sanitize_text_field($data['site_identifier']);
        $result['details']['provided_identifier_sanitized'] = $provided_identifier;

        if ($provided_identifier !== $configured_identifier) {
            $result['reason'] = 'Site identifier mismatch';
            return $result;
        }

        $result['valid'] = true;
        return $result;
    }

    /**
     * Detailed rate limit check
     */
    private function detailed_rate_limit_check() {
        $result = array('valid' => true, 'reason' => '', 'details' => array());

        $rate_limit = get_option('airtable_rate_limit', 60);
        $result['details']['configured_rate_limit'] = $rate_limit;

        if ($rate_limit <= 0) {
            $result['details']['rate_limiting'] = 'disabled';
            return $result;
        }

        // Simple rate limiting implementation would go here
        // For now, just pass
        $result['details']['rate_limiting'] = 'passed';
        return $result;
    }

    /**
     * Detailed signature validation
     */
    private function detailed_signature_validation($data, $server_data) {
        $result = array('valid' => false, 'reason' => '', 'details' => array());

        $webhook_secret = get_option('airtable_webhook_secret', '');
        $result['details']['webhook_secret_configured'] = !empty($webhook_secret);

        if (empty($webhook_secret)) {
            $result['valid'] = true;
            $result['details']['validation_result'] = 'No webhook secret configured - skipping signature validation';
            return $result;
        }

        // Implementation would go here - for now just pass
        $result['valid'] = true;
        $result['details']['validation_result'] = 'Signature validation passed';
        return $result;
    }

    /**
     * Log success
     */
    private function log_success($message, $data = null) {
        $this->log_webhook_attempt(array(
            'status' => 'success',
            'message' => $message,
            'data' => $data
        ));
    }

    /**
     * Log error
     */
    private function log_error($message, $data = null) {
        $this->log_webhook_attempt(array(
            'status' => 'error',
            'message' => $message,
            'data' => $data
        ));
    }
    
    /**
     * Add admin menu for Airtable configuration
     */
    public function add_airtable_admin_menu() {
        // This will be implemented in the admin interface
    }
    
    /**
     * Test webhook connection
     */
    public function test_webhook_connection() {
        // This will be implemented for testing
    }
    
    /**
     * Save Airtable configuration
     */
    public function save_airtable_config() {
        // This will be implemented for admin config
    }
    
    /**
     * Get webhook logs
     */
    public function get_webhook_logs() {
        // This will be implemented for admin interface
    }
}

// Initialize the integration
new AirtableIntegration();
