<?php
/**
 * Airtable Testing & Validation Tools
 * 
 * Comprehensive testing utilities for Airtable integration
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class AirtableTestingTools {
    
    private $webhook_handler;
    private $field_mapper;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->webhook_handler = new AirtableWebhookHandler();
        $this->field_mapper = new AirtableFieldMapper();
    }
    
    /**
     * Run comprehensive integration test suite
     */
    public function run_test_suite() {
        $results = array(
            'overall_status' => 'pending',
            'tests_run' => 0,
            'tests_passed' => 0,
            'tests_failed' => 0,
            'test_results' => array(),
            'summary' => '',
            'recommendations' => array()
        );
        
        // Configuration Tests
        $config_tests = $this->test_configuration();
        $results['test_results']['configuration'] = $config_tests;
        $results['tests_run'] += count($config_tests['tests']);
        $results['tests_passed'] += $config_tests['passed'];
        $results['tests_failed'] += $config_tests['failed'];
        
        // Webhook Connectivity Tests
        $webhook_tests = $this->test_webhook_connectivity();
        $results['test_results']['webhook'] = $webhook_tests;
        $results['tests_run'] += count($webhook_tests['tests']);
        $results['tests_passed'] += $webhook_tests['passed'];
        $results['tests_failed'] += $webhook_tests['failed'];
        
        // Field Mapping Tests
        $mapping_tests = $this->test_field_mapping();
        $results['test_results']['field_mapping'] = $mapping_tests;
        $results['tests_run'] += count($mapping_tests['tests']);
        $results['tests_passed'] += $mapping_tests['passed'];
        $results['tests_failed'] += $mapping_tests['failed'];
        
        // Security Tests
        $security_tests = $this->test_security();
        $results['test_results']['security'] = $security_tests;
        $results['tests_run'] += count($security_tests['tests']);
        $results['tests_passed'] += $security_tests['passed'];
        $results['tests_failed'] += $security_tests['failed'];
        
        // Data Validation Tests
        $validation_tests = $this->test_data_validation();
        $results['test_results']['validation'] = $validation_tests;
        $results['tests_run'] += count($validation_tests['tests']);
        $results['tests_passed'] += $validation_tests['passed'];
        $results['tests_failed'] += $validation_tests['failed'];
        
        // Calculate overall status
        $success_rate = $results['tests_run'] > 0 ? ($results['tests_passed'] / $results['tests_run']) * 100 : 0;
        
        if ($success_rate >= 95) {
            $results['overall_status'] = 'excellent';
        } elseif ($success_rate >= 80) {
            $results['overall_status'] = 'good';
        } elseif ($success_rate >= 60) {
            $results['overall_status'] = 'fair';
        } else {
            $results['overall_status'] = 'poor';
        }
        
        $results['success_rate'] = round($success_rate, 1);
        $results['summary'] = $this->generate_test_summary($results);
        $results['recommendations'] = $this->generate_recommendations($results);
        
        return $results;
    }
    
    /**
     * Test configuration settings
     */
    private function test_configuration() {
        $tests = array();
        $passed = 0;
        $failed = 0;
        
        // Test: Integration enabled
        $enabled = get_option('airtable_integration_enabled', false);
        $tests[] = array(
            'name' => 'Integration Enabled',
            'status' => $enabled ? 'pass' : 'fail',
            'message' => $enabled ? 'Airtable integration is enabled' : 'Airtable integration is disabled',
            'critical' => true
        );
        $enabled ? $passed++ : $failed++;
        
        // Test: API key configured
        $api_key = get_option('airtable_api_key', '');
        $api_key_valid = !empty($api_key) && strlen($api_key) >= 16;
        $tests[] = array(
            'name' => 'API Key Configuration',
            'status' => $api_key_valid ? 'pass' : 'fail',
            'message' => $api_key_valid ? 'API key is configured and valid length' : 'API key is missing or too short',
            'critical' => true
        );
        $api_key_valid ? $passed++ : $failed++;
        
        // Test: Site identifier configured
        $site_id = get_option('airtable_site_identifier', '');
        $site_id_valid = !empty($site_id) && preg_match('/^[a-z0-9-]+$/', $site_id);
        $tests[] = array(
            'name' => 'Site Identifier Format',
            'status' => $site_id_valid ? 'pass' : 'fail',
            'message' => $site_id_valid ? 'Site identifier is properly formatted' : 'Site identifier is missing or invalid format',
            'critical' => false
        );
        $site_id_valid ? $passed++ : $failed++;
        
        // Test: Rate limiting configured
        $rate_limit = get_option('airtable_rate_limit', 0);
        $rate_limit_valid = $rate_limit > 0 && $rate_limit <= 1000;
        $tests[] = array(
            'name' => 'Rate Limiting',
            'status' => $rate_limit_valid ? 'pass' : 'fail',
            'message' => $rate_limit_valid ? "Rate limit set to {$rate_limit} requests/hour" : 'Rate limit not configured or invalid',
            'critical' => false
        );
        $rate_limit_valid ? $passed++ : $failed++;
        
        // Test: Webhook secret (optional but recommended)
        $webhook_secret = get_option('airtable_webhook_secret', '');
        $secret_configured = !empty($webhook_secret);
        $tests[] = array(
            'name' => 'Webhook Secret',
            'status' => $secret_configured ? 'pass' : 'warning',
            'message' => $secret_configured ? 'Webhook secret is configured for enhanced security' : 'Webhook secret not configured (optional but recommended)',
            'critical' => false
        );
        $secret_configured ? $passed++ : $failed++;
        
        return array(
            'tests' => $tests,
            'passed' => $passed,
            'failed' => $failed,
            'category' => 'Configuration'
        );
    }
    
    /**
     * Test webhook connectivity
     */
    private function test_webhook_connectivity() {
        $tests = array();
        $passed = 0;
        $failed = 0;
        
        // Test: Webhook endpoint accessibility
        $webhook_url = admin_url('admin-ajax.php?action=airtable_webhook');
        $response = wp_remote_get($webhook_url, array('timeout' => 10));
        $accessible = !is_wp_error($response);
        $tests[] = array(
            'name' => 'Webhook Endpoint Accessibility',
            'status' => $accessible ? 'pass' : 'fail',
            'message' => $accessible ? 'Webhook endpoint is accessible' : 'Webhook endpoint is not accessible: ' . ($accessible ? '' : $response->get_error_message()),
            'critical' => true
        );
        $accessible ? $passed++ : $failed++;
        
        // Test: HTTPS availability
        $is_https = is_ssl();
        $tests[] = array(
            'name' => 'HTTPS Security',
            'status' => $is_https ? 'pass' : 'warning',
            'message' => $is_https ? 'Site is using HTTPS' : 'Site is not using HTTPS (recommended for webhooks)',
            'critical' => false
        );
        $is_https ? $passed++ : $failed++;
        
        // Test: WordPress AJAX functionality
        $ajax_test = $this->test_ajax_functionality();
        $tests[] = array(
            'name' => 'AJAX Functionality',
            'status' => $ajax_test ? 'pass' : 'fail',
            'message' => $ajax_test ? 'WordPress AJAX is functioning correctly' : 'WordPress AJAX is not working properly',
            'critical' => true
        );
        $ajax_test ? $passed++ : $failed++;
        
        return array(
            'tests' => $tests,
            'passed' => $passed,
            'failed' => $failed,
            'category' => 'Webhook Connectivity'
        );
    }
    
    /**
     * Test field mapping functionality
     */
    private function test_field_mapping() {
        $tests = array();
        $passed = 0;
        $failed = 0;
        
        // Test: Field mapper initialization
        $mapper_works = is_object($this->field_mapper);
        $tests[] = array(
            'name' => 'Field Mapper Initialization',
            'status' => $mapper_works ? 'pass' : 'fail',
            'message' => $mapper_works ? 'Field mapper initialized successfully' : 'Field mapper failed to initialize',
            'critical' => true
        );
        $mapper_works ? $passed++ : $failed++;
        
        // Test: Sample data mapping
        $sample_data = $this->get_sample_airtable_data();
        $mapped_data = $this->field_mapper->map_airtable_to_wordpress($sample_data);
        $mapping_works = !empty($mapped_data) && isset($mapped_data['business_info']);
        $tests[] = array(
            'name' => 'Sample Data Mapping',
            'status' => $mapping_works ? 'pass' : 'fail',
            'message' => $mapping_works ? 'Sample data mapped successfully' : 'Sample data mapping failed',
            'critical' => true
        );
        $mapping_works ? $passed++ : $failed++;
        
        // Test: Field validation
        if ($mapping_works) {
            $validation_result = $this->field_mapper->validate_mapped_data($mapped_data);
            $validation_works = $validation_result['valid'];
            $tests[] = array(
                'name' => 'Field Validation',
                'status' => $validation_works ? 'pass' : 'fail',
                'message' => $validation_works ? 'Field validation passed' : 'Field validation failed: ' . implode(', ', $validation_result['errors']),
                'critical' => true
            );
            $validation_works ? $passed++ : $failed++;
        }
        
        return array(
            'tests' => $tests,
            'passed' => $passed,
            'failed' => $failed,
            'category' => 'Field Mapping'
        );
    }
    
    /**
     * Test security measures
     */
    private function test_security() {
        $tests = array();
        $passed = 0;
        $failed = 0;
        
        // Test: API key validation
        $api_key_validation = $this->test_api_key_validation();
        $tests[] = array(
            'name' => 'API Key Validation',
            'status' => $api_key_validation ? 'pass' : 'fail',
            'message' => $api_key_validation ? 'API key validation working correctly' : 'API key validation failed',
            'critical' => true
        );
        $api_key_validation ? $passed++ : $failed++;
        
        // Test: Rate limiting
        $rate_limiting = $this->test_rate_limiting();
        $tests[] = array(
            'name' => 'Rate Limiting',
            'status' => $rate_limiting ? 'pass' : 'warning',
            'message' => $rate_limiting ? 'Rate limiting is functional' : 'Rate limiting may not be working correctly',
            'critical' => false
        );
        $rate_limiting ? $passed++ : $failed++;
        
        // Test: Input sanitization
        $sanitization = $this->test_input_sanitization();
        $tests[] = array(
            'name' => 'Input Sanitization',
            'status' => $sanitization ? 'pass' : 'fail',
            'message' => $sanitization ? 'Input sanitization working correctly' : 'Input sanitization failed',
            'critical' => true
        );
        $sanitization ? $passed++ : $failed++;
        
        return array(
            'tests' => $tests,
            'passed' => $passed,
            'failed' => $failed,
            'category' => 'Security'
        );
    }
    
    /**
     * Test data validation
     */
    private function test_data_validation() {
        $tests = array();
        $passed = 0;
        $failed = 0;
        
        // Test: Required field validation
        $required_validation = $this->test_required_fields();
        $tests[] = array(
            'name' => 'Required Field Validation',
            'status' => $required_validation ? 'pass' : 'fail',
            'message' => $required_validation ? 'Required field validation working' : 'Required field validation failed',
            'critical' => true
        );
        $required_validation ? $passed++ : $failed++;
        
        // Test: Character limit validation
        $length_validation = $this->test_character_limits();
        $tests[] = array(
            'name' => 'Character Limit Validation',
            'status' => $length_validation ? 'pass' : 'fail',
            'message' => $length_validation ? 'Character limit validation working' : 'Character limit validation failed',
            'critical' => false
        );
        $length_validation ? $passed++ : $failed++;
        
        // Test: Color code validation
        $color_validation = $this->test_color_validation();
        $tests[] = array(
            'name' => 'Color Code Validation',
            'status' => $color_validation ? 'pass' : 'fail',
            'message' => $color_validation ? 'Color code validation working' : 'Color code validation failed',
            'critical' => false
        );
        $color_validation ? $passed++ : $failed++;
        
        return array(
            'tests' => $tests,
            'passed' => $passed,
            'failed' => $failed,
            'category' => 'Data Validation'
        );
    }
    
    /**
     * Generate test summary
     */
    private function generate_test_summary($results) {
        $total = $results['tests_run'];
        $passed = $results['tests_passed'];
        $failed = $results['tests_failed'];
        $rate = $results['success_rate'];
        
        return "Ran {$total} tests: {$passed} passed, {$failed} failed. Success rate: {$rate}%";
    }
    
    /**
     * Generate recommendations based on test results
     */
    private function generate_recommendations($results) {
        $recommendations = array();
        
        foreach ($results['test_results'] as $category => $category_results) {
            foreach ($category_results['tests'] as $test) {
                if ($test['status'] === 'fail' && $test['critical']) {
                    $recommendations[] = "CRITICAL: Fix {$test['name']} - {$test['message']}";
                } elseif ($test['status'] === 'fail') {
                    $recommendations[] = "Fix {$test['name']} - {$test['message']}";
                } elseif ($test['status'] === 'warning') {
                    $recommendations[] = "Consider: {$test['name']} - {$test['message']}";
                }
            }
        }
        
        if (empty($recommendations)) {
            $recommendations[] = "All tests passed! Your Airtable integration is properly configured.";
        }
        
        return $recommendations;
    }
    
    /**
     * Helper methods for specific tests
     */
    private function test_ajax_functionality() {
        // Test basic AJAX functionality
        return function_exists('wp_ajax_nopriv_airtable_webhook');
    }
    
    private function get_sample_airtable_data() {
        return array(
            'business_name' => 'Test Repair Shop',
            'city' => 'Test City',
            'state' => 'FL',
            'hero_heading' => 'Test Hero Heading',
            'primary_color' => '#165C9C'
        );
    }
    
    private function test_api_key_validation() {
        // Test API key validation logic
        $test_data = array('api_key' => get_option('airtable_api_key', ''));
        return $this->webhook_handler->validate_webhook($test_data, $_SERVER);
    }
    
    private function test_rate_limiting() {
        // Test rate limiting functionality
        return method_exists($this->webhook_handler, 'check_rate_limit');
    }
    
    private function test_input_sanitization() {
        // Test input sanitization
        $test_data = array('test_field' => '<script>alert("test")</script>');
        $sanitized = $this->webhook_handler->sanitize_webhook_data($test_data);
        return !strpos($sanitized['test_field'], '<script>');
    }
    
    private function test_required_fields() {
        // Test required field validation
        $test_data = array();
        $validation = $this->webhook_handler->validate_required_fields($test_data);
        return !$validation['valid']; // Should fail with empty data
    }
    
    private function test_character_limits() {
        // Test character limit validation
        $test_data = array('hero_heading' => str_repeat('a', 200)); // Too long
        $validation = $this->webhook_handler->validate_field_lengths($test_data);
        return !$validation['valid']; // Should fail with too long text
    }
    
    private function test_color_validation() {
        // Test color code validation
        $valid_color = '#FF0000';
        $invalid_color = 'red';
        
        $sanitized_valid = $this->webhook_handler->sanitize_webhook_data(array('primary_color' => $valid_color));
        $sanitized_invalid = $this->webhook_handler->sanitize_webhook_data(array('primary_color' => $invalid_color));
        
        return $sanitized_valid['primary_color'] === $valid_color && $sanitized_invalid['primary_color'] !== $invalid_color;
    }
}
