<?php
/**
 * Plugin Name: RepairLift WP Customizer
 * Plugin URI: https://repairlift.com
 * Description: A comprehensive website customizer for repair shops and local businesses with visual editing capabilities, multi-page management, and professional design system.
 * Version: 2.0.0
 * Author: RepairLift Team
 * License: GPL v2 or later
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}



class WebsiteGeneratorPro {
    
    public function __construct() {
        add_action('init', array($this, 'init'));
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('wp_ajax_generate_website', array($this, 'handle_form_submission'));
        add_action('wp_ajax_create_backup', array($this, 'handle_create_backup'));
        add_action('wp_ajax_restore_backup', array($this, 'handle_restore_backup'));
        add_action('wp_ajax_get_all_backups', array($this, 'handle_get_all_backups'));
        add_action('wp_ajax_delete_backup', array($this, 'handle_delete_backup'));
        add_action('wp_ajax_get_text_content', array($this, 'handle_get_text_content'));
        add_action('wp_ajax_update_text_content', array($this, 'handle_update_text_content'));
        add_action('wp_ajax_get_unused_assets', array($this, 'handle_get_unused_assets'));
        add_action('wp_ajax_get_current_content', array($this, 'handle_get_current_content'));
        add_action('wp_ajax_apply_visual_changes', array($this, 'handle_apply_visual_changes'));
        add_action('wp_ajax_upload_logo', array($this, 'handle_upload_logo'));
        add_action('wp_ajax_upload_logo_enhanced', array($this, 'handle_upload_logo_enhanced'));
        add_action('wp_ajax_restore_previous_logo', array($this, 'handle_restore_previous_logo'));
        add_action('wp_ajax_remove_current_logo', array($this, 'handle_remove_current_logo'));
        add_action('wp_ajax_detect_website_colors', array($this, 'handle_detect_website_colors'));
        add_action('wp_ajax_revert_design_changes', array($this, 'handle_revert_design_changes'));

        // Business Info AJAX handlers
        add_action('wp_ajax_get_business_info', array($this, 'handle_get_business_info'));
        add_action('wp_ajax_save_business_info', array($this, 'handle_save_business_info'));
        add_action('wp_ajax_preview_ai_content', array($this, 'handle_preview_ai_content'));
        add_action('wp_ajax_clear_business_info', array($this, 'handle_clear_business_info'));

        // Section-specific AJAX handlers
        add_action('wp_ajax_apply_section_changes', array($this, 'handle_apply_section_changes'));
        add_action('wp_ajax_preview_section_changes', array($this, 'handle_preview_section_changes'));

        // AI Configuration AJAX handlers
        add_action('wp_ajax_save_ai_config', array($this, 'handle_save_ai_config'));
        add_action('wp_ajax_test_ai_connection', array($this, 'handle_test_ai_connection'));
        add_action('wp_ajax_clear_ai_config', array($this, 'handle_clear_ai_config'));
        add_action('wp_ajax_save_ai_prompt', array($this, 'handle_save_ai_prompt'));
        add_action('wp_ajax_test_ai_prompt', array($this, 'handle_test_ai_prompt'));
        add_action('wp_ajax_get_ai_usage_stats', array($this, 'handle_get_ai_usage_stats'));
        add_action('wp_ajax_generate_ai_content', array($this, 'handle_generate_ai_content'));
        add_action('wp_ajax_get_prompt_templates', array($this, 'handle_get_prompt_templates'));
        add_action('wp_ajax_get_ai_generation_history', array($this, 'handle_get_ai_generation_history'));
        add_action('wp_ajax_clear_ai_generation_history', array($this, 'handle_clear_ai_generation_history'));
        add_action('wp_ajax_save_business_questionnaire', array($this, 'handle_save_business_questionnaire'));

        // Debug/Maintenance AJAX handlers
        add_action('wp_ajax_restore_default_content', array($this, 'handle_restore_default_content'));
        add_action('wp_ajax_restore_field_default', array($this, 'handle_restore_field_default'));

        // AI Image Generation AJAX handlers are in class-gemini-image-generator.php

        // Airtable Integration AJAX handlers
        add_action('wp_ajax_save_airtable_config', array($this, 'handle_save_airtable_config'));
        add_action('wp_ajax_test_airtable_webhook', array($this, 'handle_test_airtable_webhook'));
        add_action('wp_ajax_get_airtable_logs', array($this, 'handle_get_airtable_logs'));
        add_action('wp_ajax_clear_airtable_logs', array($this, 'handle_clear_airtable_logs'));
        add_action('wp_ajax_run_airtable_test_suite', array($this, 'handle_run_airtable_test_suite'));
        add_action('wp_ajax_check_airtable_database_data', array($this, 'handle_check_airtable_database_data'));
        add_action('wp_ajax_test_content_block_manager', array($this, 'handle_test_content_block_manager'));

        add_action('admin_enqueue_scripts', array($this, 'enqueue_scripts'));
        register_activation_hook(__FILE__, array($this, 'activate'));
    }
    
    public function init() {
        // Load dependencies
        require_once plugin_dir_path(__FILE__) . 'includes/theme-customizer.php';
        require_once plugin_dir_path(__FILE__) . 'includes/content-manager.php';
        require_once plugin_dir_path(__FILE__) . 'includes/text-manager.php';

        // Backup system is now integrated into ContentManager

        // Load AI system
        require_once plugin_dir_path(__FILE__) . 'includes/ai/class-business-info-collector.php';
        require_once plugin_dir_path(__FILE__) . 'includes/ai/class-claude-ai-manager.php';
        require_once plugin_dir_path(__FILE__) . 'includes/ai/class-prompt-manager.php';
        require_once plugin_dir_path(__FILE__) . 'includes/ai/class-gemini-image-generator.php';

        // Load Airtable integration
        require_once plugin_dir_path(__FILE__) . 'includes/airtable-integration.php';
        require_once plugin_dir_path(__FILE__) . 'includes/airtable-testing-tools.php';

        // Initialize Gemini Image Generator (for AJAX handlers)
        if (is_admin()) {
            new Gemini_Image_Generator();
        }

        // Initialize text replacement on frontend
        if (!is_admin()) {
            $text_manager = new TextManager();
            $text_manager->apply_custom_texts();
        }

        // Enqueue frontend styles for design system (Safe Mode)
        add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_styles'), 999);

        // Add body class for CSS specificity
        add_filter('body_class', array($this, 'add_body_class'));

        // Initialize backup system
        $this->init_backup_system();
    }
    
    /**
     * Enqueue frontend styles for design system
     */
    public function enqueue_frontend_styles() {
        $custom_css_path = get_stylesheet_directory() . '/website-generator-custom.css';
        $custom_css_url = get_stylesheet_directory_uri() . '/website-generator-custom.css';

        if (file_exists($custom_css_path)) {
            // Enqueue with high priority and dependencies to ensure it loads last
            wp_enqueue_style(
                'website-generator-custom',
                $custom_css_url,
                array('wp-block-library', 'global-styles'), // Load after core styles
                filemtime($custom_css_path)
            );

            // Add inline style to ensure our CSS has maximum priority
            $inline_css = "
/* Website Generator - Priority Enforcement */
body.website-generator-active {
    /* This class ensures our styles take precedence */
}";
            wp_add_inline_style('website-generator-custom', $inline_css);

            error_log('Website Generator: Custom CSS enqueued successfully');
        } else {
            error_log('Website Generator: Custom CSS file not found at: ' . $custom_css_path);
        }
    }

    /**
     * Add body class for CSS specificity
     */
    public function add_body_class($classes) {
        $classes[] = 'website-generator-active';
        return $classes;
    }

    public function add_admin_menu() {
        add_menu_page(
            'RepairLift WP Customizer',
            'RepairLift Customizer',
            'manage_options',
            'website-generator',
            array($this, 'admin_page'),
            'dashicons-admin-site-alt3',
            30
        );
    }
    
    public function admin_page() {
        // Include the admin page file
        include_once plugin_dir_path(__FILE__) . 'admin/admin-page.php';
    }
    
    public function enqueue_scripts($hook) {
        if ($hook !== 'toplevel_page_website-generator') {
            return;
        }
        
        wp_enqueue_script(
            'website-generator-js',
            plugin_dir_url(__FILE__) . 'admin/admin-scripts.js',
            array('jquery'),
            '1.0.1',
            true
        );

        // Enqueue business questionnaire JavaScript
        wp_enqueue_script(
            'website-generator-business-questionnaire',
            plugin_dir_url(__FILE__) . 'admin/business-questionnaire.js',
            array('jquery', 'website-generator-js'),
            '1.0.0',
            true
        );

        // Enqueue AI regeneration JavaScript
        wp_enqueue_script(
            'website-generator-ai-regeneration',
            plugin_dir_url(__FILE__) . 'admin/ai-regeneration.js',
            array('jquery', 'website-generator-js'),
            '1.0.0',
            true
        );

        wp_enqueue_style(
            'website-generator-css',
            plugin_dir_url(__FILE__) . 'admin/admin-styles.css',
            array(),
            '1.0.1'
        );
        
        wp_localize_script('website-generator-js', 'ajax_object', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('website_generator_nonce')
        ));

        // Also localize the main script with website_generator_ajax for AI Images compatibility
        wp_localize_script('website-generator-js', 'website_generator_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('website_generator_nonce')
        ));

        // Localize script for business info
        wp_localize_script('website-generator-business-info', 'website_generator_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('website_generator_nonce')
        ));

        // Localize script for AI regeneration
        wp_localize_script('website-generator-ai-regeneration', 'website_generator_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('website_generator_nonce')
        ));

        // Enqueue Airtable configuration script
        wp_enqueue_script(
            'website-generator-airtable-config',
            plugin_dir_url(__FILE__) . 'admin/airtable-config.js',
            array('jquery'),
            '1.0.0',
            true
        );

        // Localize script for Airtable configuration
        wp_localize_script('website-generator-airtable-config', 'website_generator_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('website_generator_nonce')
        ));
    }
    
    public function handle_form_submission() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'website_generator_nonce')) {
            wp_die('Security check failed');
        }
        
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }
        
        $customizer = new ThemeCustomizer();
        $content_manager = new ContentManager();
        
        $result = array();
        
        try {
            // Update colors
            if (isset($_POST['primary_color']) && isset($_POST['secondary_color'])) {
                $result['colors'] = $customizer->update_colors(
                    $_POST['primary_color'],
                    $_POST['secondary_color']
                );
            }
            
            // Handle logo upload
            if (isset($_FILES['logo'])) {
                $result['logo'] = $customizer->update_logo($_FILES['logo']);
            }
            
            // Update site identity
            if (isset($_POST['company_name'])) {
                $result['identity'] = $content_manager->update_site_identity(
                    $_POST['company_name'],
                    $_POST['tagline'] ?? ''
                );
            }
            
            // Update content
            if (isset($_POST['homepage_content'])) {
                $result['content'] = $content_manager->update_homepage_content($_POST);
            }
            
            wp_send_json_success($result);
            
        } catch (Exception $e) {
            wp_send_json_error('Error: ' . $e->getMessage());
        }
    }
    
    public function handle_create_backup() {
        check_ajax_referer('website_generator_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        try {
            $description = sanitize_text_field($_POST['description'] ?? '');
            error_log('Website Generator: Creating backup with description: ' . $description);

            $content_manager = new ContentManager();
            $result = $content_manager->create_backup($description);

            error_log('Website Generator: Backup creation result: ' . print_r($result, true));

            if ($result['status'] === 'success') {
                // Return the backup_id and message in the expected format
                wp_send_json_success(array(
                    'backup_id' => $result['backup_id'],
                    'message' => $result['message']
                ));
            } else {
                wp_send_json_error($result['message']);
            }

        } catch (Exception $e) {
            wp_send_json_error('Failed to create backup: ' . $e->getMessage());
        }
    }

    public function handle_get_all_backups() {
        check_ajax_referer('website_generator_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        error_log('Website Generator: Getting all backups');

        $content_manager = new ContentManager();
        $backups_raw = $content_manager->get_backups();

        error_log('Website Generator: Raw backups count: ' . count($backups_raw));

        // Convert backup data to format expected by frontend
        $backups = array();
        foreach ($backups_raw as $backup_id => $backup_data) {
            $backups[] = array(
                'backup_id' => $backup_id,
                'description' => $backup_data['description'] ?? '',
                'created_at' => $backup_data['timestamp'] ?? '',
                'version' => $backup_data['version'] ?? '2.0.0',
                'checksum' => $backup_data['checksum'] ?? ''
            );
        }

        wp_send_json_success(array(
            'backups' => $backups,
            'count' => count($backups)
        ));
    }

    public function handle_restore_backup() {
        check_ajax_referer('website_generator_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        try {
            $backup_id = sanitize_text_field($_POST['backup_id']);

            if (empty($backup_id)) {
                wp_send_json_error('Backup ID is required');
            }

            $content_manager = new ContentManager();
            $result = $content_manager->restore_backup($backup_id);

            if ($result['status'] === 'success') {
                wp_send_json_success($result);
            } else {
                wp_send_json_error($result['message']);
            }

        } catch (Exception $e) {
            wp_send_json_error('Failed to restore backup: ' . $e->getMessage());
        }
    }

    /**
     * Handle delete backup AJAX request
     */
    public function handle_delete_backup() {
        check_ajax_referer('website_generator_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        try {
            $backup_id = sanitize_text_field($_POST['backup_id']);

            if (empty($backup_id)) {
                wp_send_json_error('Backup ID is required');
            }

            $content_manager = new ContentManager();
            $result = $content_manager->delete_backup($backup_id);

            if ($result['status'] === 'success') {
                wp_send_json_success($result);
            } else {
                wp_send_json_error($result['message']);
            }

        } catch (Exception $e) {
            wp_send_json_error('Failed to delete backup: ' . $e->getMessage());
        }
    }

    public function handle_get_text_content() {
        check_ajax_referer('website_generator_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        $text_manager = new TextManager();
        $content = $text_manager->get_all_text_content();

        wp_send_json_success($content);
    }

    public function handle_update_text_content() {
        check_ajax_referer('website_generator_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        $updates = $_POST['updates'];
        $text_manager = new TextManager();
        $result = $text_manager->update_text_content($updates);

        wp_send_json($result);
    }

    public function handle_get_unused_assets() {
        check_ajax_referer('website_generator_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        $text_manager = new TextManager();
        $unused_assets = $text_manager->get_unused_assets();

        wp_send_json_success($unused_assets);
    }

    /**
     * Handle get current content AJAX request
     */
    public function handle_get_current_content() {
        check_ajax_referer('website_generator_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        $current_content = $this->get_current_content();

        error_log('Website Generator: handle_get_current_content() returning: ' . json_encode($current_content));

        wp_send_json_success($current_content);
    }

    /**
     * Handle apply visual changes AJAX request
     */
    public function handle_apply_visual_changes() {
        check_ajax_referer('website_generator_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        $blocks = $_POST['blocks'] ?? array();
        $result = $this->apply_visual_changes($blocks);

        wp_send_json_success($result);
    }

    /**
     * Handle enhanced logo upload AJAX request with WebP conversion
     */
    public function handle_upload_logo_enhanced() {
        check_ajax_referer('website_generator_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        if (!isset($_FILES['logo_upload'])) {
            wp_send_json_error('No file uploaded');
        }

        $result = $this->process_logo_upload_enhanced($_FILES['logo_upload']);

        if ($result['status'] === 'success') {
            wp_send_json_success($result);
        } else {
            wp_send_json_error($result['message']);
        }
    }

    /**
     * Handle logo upload AJAX request
     */
    public function handle_upload_logo() {
        check_ajax_referer('website_generator_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        if (!isset($_FILES['logo_upload'])) {
            wp_send_json_error('No file uploaded');
        }

        $result = $this->process_logo_upload($_FILES['logo_upload']);

        if ($result['status'] === 'success') {
            wp_send_json_success($result);
        } else {
            wp_send_json_error($result['message']);
        }
    }

    /**
     * Handle restore previous logo AJAX request
     */
    public function handle_restore_previous_logo() {
        check_ajax_referer('website_generator_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        $result = $this->restore_previous_logo();

        if ($result['status'] === 'success') {
            wp_send_json_success($result);
        } else {
            wp_send_json_error($result['message']);
        }
    }

    /**
     * Handle remove current logo AJAX request
     */
    public function handle_remove_current_logo() {
        check_ajax_referer('website_generator_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        $result = $this->remove_current_logo();

        if ($result['status'] === 'success') {
            wp_send_json_success($result);
        } else {
            wp_send_json_error($result['message']);
        }
    }

    /**
     * Handle revert design changes AJAX request
     */
    public function handle_revert_design_changes() {
        check_ajax_referer('website_generator_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        // Also restore previous logo when reverting all changes
        $this->restore_previous_logo();

        $customizer = new ThemeCustomizer();
        $result = $customizer->revert_all_changes();

        if ($result['status'] === 'success') {
            wp_send_json_success($result);
        } else {
            wp_send_json_error($result);
        }
    }

    /**
     * Get current content from homepage
     */
    private function get_current_content() {
        $homepage_id = get_option('page_on_front');

        if (!$homepage_id) {
            return array('error' => 'No homepage set');
        }

        $homepage = get_post($homepage_id);
        if (!$homepage) {
            return array('error' => 'Homepage not found');
        }

        // Parse current content and extract text
        $content = $homepage->post_content;

        // Extract hero section content
        $hero = array(
            'heading' => $this->extract_text_between($content, '<h1', '</h1>'),
            'tagline' => $this->extract_tagline($content),
            'button1' => 'Start a Repair',
            'button2' => 'Call Us Now'
        );

        // Extract other sections
        $onestop = array(
            'heading' => 'Your One-Stop Shop Repair Store',
            'description' => $this->extract_onestop_description($content),
            'button' => 'Our Location'
        );

        $contact = array(
            'phone' => $this->extract_phone($content),
            'email' => $this->extract_email($content),
            'address' => $this->extract_address($content)
        );

        // Get current design system settings
        $design = $this->get_current_design_system();
        error_log('Website Generator: get_current_content() design data: ' . json_encode($design));

        // Get current logo information
        $logo = $this->get_current_logo();

        $result = array(
            'hero' => $hero,
            'onestop' => $onestop,
            'contact' => $contact,
            'design' => $design,
            'logo' => $logo
        );

        error_log('Website Generator: get_current_content() final result: ' . json_encode($result));
        return $result;
    }

    /**
     * Apply visual changes to content
     */
    private function apply_visual_changes($blocks) {
        $content_manager = new ContentManager();

        // Convert blocks data to format expected by content manager
        $data = array();

        if (isset($blocks['hero'])) {
            $data['company_name'] = $blocks['hero']['heading'] ?? '';
            $data['tagline'] = $blocks['hero']['tagline'] ?? '';
        }

        if (isset($blocks['contact'])) {
            $data['phone'] = $blocks['contact']['phone'] ?? '';
            $data['email'] = $blocks['contact']['email'] ?? '';
            $data['address'] = $blocks['contact']['address'] ?? '';
        }

        if (isset($blocks['onestop'])) {
            $data['about_text'] = $blocks['onestop']['description'] ?? '';
        }

        // Handle design system changes
        if (isset($blocks['design'])) {
            $this->update_design_system($blocks['design']);
        }

        // Apply changes using existing content manager
        return $content_manager->update_homepage_content($data);
    }

    /**
     * Helper function to extract text between HTML tags
     */
    private function extract_text_between($content, $start_tag, $end_tag) {
        $pattern = '/' . preg_quote($start_tag, '/') . '[^>]*>(.*?)' . preg_quote($end_tag, '/') . '/s';
        if (preg_match($pattern, $content, $matches)) {
            $text = strip_tags($matches[1]);
            // Clean up any duplicated text that might have been added by JavaScript
            $text = $this->clean_duplicated_text($text);
            return $text;
        }
        return '';
    }

    /**
     * Clean up duplicated text that might have been added by JavaScript replacements
     */
    private function clean_duplicated_text($text) {
        // Remove all instances of "Device Repair in Your City" (case insensitive)
        $text = preg_replace('/Device Repair in Your City\s*/i', '', $text);

        // Remove any trailing/leading whitespace
        $text = trim($text);

        // If text contains the original pattern, extract just the clean version
        if (stripos($text, 'Premier Device Repair in Anytown, FL') !== false) {
            return 'Premier Device Repair in Anytown, FL';
        }

        // Additional cleanup for any remaining duplicated patterns
        $text = preg_replace('/\s+/', ' ', $text); // Replace multiple spaces with single space
        $text = preg_replace('/(.+?)\1+/', '$1', $text); // Remove repeated patterns

        return $text;
    }

    /**
     * Extract tagline from content
     */
    private function extract_tagline($content) {
        // Look for the specific tagline pattern
        if (strpos($content, 'Smartphones | Tablets | Computers') !== false) {
            return 'Smartphones | Tablets | Computers | & More';
        }
        return '';
    }

    /**
     * Extract one-stop shop description
     */
    private function extract_onestop_description($content) {
        if (strpos($content, 'Same Day Service') !== false) {
            return 'Same Day Service on Major Brands & Devices. Visit us at a location near you. We take pride in what we do. And what we do best is restore your device back to its original condition. You can rest easy when you have your device repaired with us. We offer a warranty on all our repairs.';
        }
        return '';
    }

    /**
     * Extract phone number from content
     */
    private function extract_phone($content) {
        // First try to find the phone number in the content
        if (preg_match('/\(555\) 222-1111/', $content)) {
            return '(*************';
        }

        // If not found in content, check if it's stored in custom texts
        $custom_texts = get_option('website_generator_custom_texts', array());
        if (isset($custom_texts['phone_number']) && !empty($custom_texts['phone_number'])) {
            return $custom_texts['phone_number'];
        }

        // Default fallback
        return '(*************';
    }

    /**
     * Extract email from content
     */
    private function extract_email($content) {
        // First try to find email in content
        if (preg_match('/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/', $content, $matches)) {
            return $matches[0];
        }

        // If not found in content, check if it's stored in custom texts
        $custom_texts = get_option('website_generator_custom_texts', array());
        if (isset($custom_texts['email']) && !empty($custom_texts['email'])) {
            return $custom_texts['email'];
        }

        // Default fallback
        return '<EMAIL>';
    }

    /**
     * Extract address from content
     */
    private function extract_address($content) {
        // First try to find address in content
        if (strpos($content, '123 Main Street') !== false) {
            return '123 Main Street, Anytown, FL 12345';
        }

        // If not found in content, check if it's stored in custom texts
        $custom_texts = get_option('website_generator_custom_texts', array());
        if (isset($custom_texts['address']) && !empty($custom_texts['address'])) {
            return $custom_texts['address'];
        }

        // Default fallback
        return '123 Main Street, Anytown, FL 12345';
    }

    /**
     * Get current design system settings
     */
    private function get_current_design_system() {
        // Always use our detected actual colors (which include the correct fallbacks)
        $actual_colors = $this->detect_actual_website_colors();

        error_log('Website Generator: get_current_design_system() returning colors: ' . json_encode($actual_colors));

        $design_system = array(
            'primary_color' => $actual_colors['primary'],
            'secondary_color' => $actual_colors['secondary'],
            'accent_color' => $actual_colors['tertiary'],
            'button_style' => get_option('website_generator_button_style', 'pill'),
            'button_size' => get_option('website_generator_button_size', 'medium'),
            'button_text_color' => $actual_colors['button_text']
        );

        error_log('Website Generator: Final design system: ' . json_encode($design_system));
        return $design_system;
    }

    /**
     * Get theme.json colors
     */
    private function get_theme_colors() {
        // Get theme.json content
        $theme_json_path = get_stylesheet_directory() . '/theme.json';

        if (!file_exists($theme_json_path)) {
            return array();
        }

        $theme_json = json_decode(file_get_contents($theme_json_path), true);

        if (!$theme_json || !isset($theme_json['settings']['color']['palette'])) {
            return array();
        }

        $palette = $theme_json['settings']['color']['palette'];
        $colors = array();

        foreach ($palette as $color) {
            $colors[$color['slug']] = $color['color'];
        }

        return $colors;
    }

    /**
     * Detect actual colors being used on the website
     */
    private function detect_actual_website_colors() {
        // Get the actual colors from WordPress theme system
        $actual_colors = $this->get_wordpress_theme_colors();

        // If detection fails, use our known correct colors from website analysis
        if (empty($actual_colors)) {
            return array(
                'primary' => '#165C9C',    // Actual button background color (verified from website)
                'secondary' => '#111111',  // Actual text color (verified from website)
                'tertiary' => '#FFFFFF',   // Actual background color (verified from website)
                'button_text' => '#FFFFFF' // Actual button text color (verified from website)
            );
        }

        return $actual_colors;
    }

    /**
     * Get actual colors from WordPress theme system
     */
    private function get_wordpress_theme_colors() {
        // Check if we have previously saved detected colors
        $saved_colors = get_option('website_generator_detected_colors', array());

        if (!empty($saved_colors)) {
            error_log('Website Generator: Using saved detected colors: ' . json_encode($saved_colors));
            return $saved_colors;
        }

        // Try to get colors from theme.json first
        $theme_colors = $this->get_theme_colors();

        if (!empty($theme_colors)) {
            // If theme.json has colors, but they're the wrong green ones, override them
            if (isset($theme_colors['primary']) && $theme_colors['primary'] === '#9dff20') {
                error_log('Website Generator: Overriding incorrect theme.json green colors with actual website colors');
                return array(
                    'primary' => '#165C9C',    // Actual website primary (blue)
                    'secondary' => '#111111',  // Actual website text (dark gray)
                    'tertiary' => '#FFFFFF',   // Actual website background (white)
                    'button_text' => '#FFFFFF' // Actual button text (white)
                );
            }
        }

        // Fallback to our verified actual colors
        error_log('Website Generator: Using verified actual website colors');
        return array(
            'primary' => '#165C9C',    // Verified from website inspection
            'secondary' => '#111111',  // Verified from website inspection
            'tertiary' => '#FFFFFF',   // Verified from website inspection
            'button_text' => '#FFFFFF' // Verified from website inspection
        );
    }

    /**
     * Get default design system settings
     */
    private function get_default_design_system() {
        return array(
            'primary_color' => '#165C9C',      // Actual website primary (blue)
            'secondary_color' => '#6AA7E0',    // Actual website secondary (light blue)
            'accent_color' => '#F7F7F7',       // Actual website tertiary (light gray)
            'button_style' => 'pill',
            'button_size' => 'medium',
            'button_text_color' => '#FFFFFF'   // Actual button text color (white)
        );
    }

    /**
     * Update design system settings
     */
    private function update_design_system($design_data) {
        // Save all design preferences FIRST (before CSS generation)
        update_option('website_generator_primary_color', $design_data['primary_color'] ?? '#165C9C');
        update_option('website_generator_secondary_color', $design_data['secondary_color'] ?? '#111111');
        update_option('website_generator_accent_color', $design_data['accent_color'] ?? '#FFFFFF');
        update_option('website_generator_button_style', $design_data['button_style'] ?? 'pill');
        update_option('website_generator_button_size', $design_data['button_size'] ?? 'medium');
        update_option('website_generator_button_text_color', $design_data['button_text_color'] ?? '#FFFFFF');
        update_option('website_generator_typography_heading', $design_data['typography_heading'] ?? 'font-modern');
        update_option('website_generator_typography_body', $design_data['typography_body'] ?? 'font-modern');
        update_option('website_generator_hero_animation', $design_data['hero_animation'] ?? 'fadeInUp');
        update_option('website_generator_image_animation', $design_data['image_animation'] ?? 'fadeIn');
        update_option('website_generator_animation_speed', $design_data['animation_speed'] ?? 'normal');
        update_option('website_generator_section_spacing', $design_data['section_spacing'] ?? 'normal');

        // Use enhanced theme customizer to update colors AND regenerate CSS
        $customizer = new ThemeCustomizer();

        try {
            // This will read the updated options from database and regenerate CSS
            $result = $customizer->update_colors(
                $design_data['primary_color'] ?? '#165C9C',
                $design_data['secondary_color'] ?? '#111111',
                $design_data['button_text_color'] ?? '#FFFFFF'
            );

            error_log('Website Generator: CSS regeneration result: ' . print_r($result, true));

        } catch (Exception $e) {
            error_log('Website Generator: CSS regeneration failed: ' . $e->getMessage());
            return array(
                'status' => 'error',
                'message' => 'Failed to update colors and regenerate CSS: ' . $e->getMessage()
            );
        }

        return array(
            'status' => 'success',
            'message' => 'Design system updated and CSS regenerated successfully'
        );
    }



    /**
     * Convert image to WebP format
     */
    private function convert_to_webp($source_file, $quality = 80) {
        // Check if GD supports WebP
        if (!function_exists('imagewebp')) {
            return false;
        }

        $image_info = getimagesize($source_file);
        if (!$image_info) {
            return false;
        }

        $mime_type = $image_info['mime'];

        // Create image resource based on type
        switch ($mime_type) {
            case 'image/jpeg':
                $image = imagecreatefromjpeg($source_file);
                break;
            case 'image/png':
                $image = imagecreatefrompng($source_file);
                // Preserve transparency for PNG
                imagealphablending($image, false);
                imagesavealpha($image, true);
                break;
            default:
                return false;
        }

        if (!$image) {
            return false;
        }

        // Generate WebP filename
        $path_info = pathinfo($source_file);
        $webp_file = $path_info['dirname'] . '/' . $path_info['filename'] . '.webp';

        // Convert to WebP
        $success = imagewebp($image, $webp_file, $quality);
        imagedestroy($image);

        if ($success && file_exists($webp_file)) {
            $original_size = filesize($source_file);
            $webp_size = filesize($webp_file);

            return array(
                'webp_file' => $webp_file,
                'webp_url' => str_replace(wp_upload_dir()['basedir'], wp_upload_dir()['baseurl'], $webp_file),
                'original_size' => $original_size,
                'webp_size' => $webp_size,
                'compression_ratio' => $original_size > 0 ? round((1 - $webp_size / $original_size) * 100, 1) : 0
            );
        }

        return false;
    }

    /**
     * Enhanced logo upload processing with WebP conversion
     */
    private function process_logo_upload_enhanced($file) {
        // Original upload processing
        $result = $this->process_logo_upload($file);

        if ($result['status'] === 'success') {
            // Attempt WebP conversion for JPG/PNG
            if (in_array($file['type'], ['image/jpeg', 'image/png'])) {
                $original_file = get_attached_file($result['attachment_id']);
                $webp_result = $this->convert_to_webp($original_file);

                if ($webp_result) {
                    // Create WebP attachment
                    $webp_attachment = array(
                        'post_mime_type' => 'image/webp',
                        'post_title' => 'Company Logo (WebP) - ' . date('Y-m-d H:i:s'),
                        'post_content' => '',
                        'post_status' => 'inherit',
                        'post_parent' => $result['attachment_id'] // Link to original
                    );

                    $webp_attach_id = wp_insert_attachment($webp_attachment, $webp_result['webp_file']);

                    if (!is_wp_error($webp_attach_id)) {
                        // Generate attachment metadata for WebP
                        if (!function_exists('wp_generate_attachment_metadata')) {
                            require_once(ABSPATH . 'wp-admin/includes/image.php');
                        }

                        $webp_attach_data = wp_generate_attachment_metadata($webp_attach_id, $webp_result['webp_file']);
                        wp_update_attachment_metadata($webp_attach_id, $webp_attach_data);

                        $result['webp'] = array(
                            'attachment_id' => $webp_attach_id,
                            'url' => $webp_result['webp_url'],
                            'file_size' => $webp_result['webp_size'],
                            'compression_saved' => $webp_result['compression_ratio'],
                            'original_size' => $webp_result['original_size']
                        );

                        $result['message'] .= ' WebP version created with ' . $webp_result['compression_ratio'] . '% size reduction.';
                    }
                }
            }
        }

        return $result;
    }

    /**
     * Restore previous logo from backup
     */
    private function restore_previous_logo() {
        // Get the most recent backup
        $backups = get_option('website_generator_backups', array());

        if (empty($backups)) {
            return array(
                'status' => 'error',
                'message' => 'No backup found to restore logo from'
            );
        }

        // Get the most recent backup
        $latest_backup = end($backups);

        if (!isset($latest_backup['site_logo']) || empty($latest_backup['site_logo'])) {
            return array(
                'status' => 'error',
                'message' => 'No previous logo found in backup'
            );
        }

        // Restore the logo
        set_theme_mod('custom_logo', $latest_backup['site_logo']);

        return array(
            'status' => 'success',
            'message' => 'Previous logo restored successfully'
        );
    }

    /**
     * Remove current logo
     */
    private function remove_current_logo() {
        // Remove the custom logo theme mod
        remove_theme_mod('custom_logo');

        return array(
            'status' => 'success',
            'message' => 'Logo removed successfully'
        );
    }

    /**
     * Process logo upload
     */
    private function process_logo_upload($file) {
        // Validate file
        $allowed_types = array('image/jpeg', 'image/png', 'image/gif', 'image/svg+xml', 'image/webp');
        if (!in_array($file['type'], $allowed_types)) {
            return array(
                'status' => 'error',
                'message' => 'Invalid file type. Please upload PNG, JPG, SVG, or WebP.'
            );
        }

        // Check file size (10MB limit)
        if ($file['size'] > 10 * 1024 * 1024) {
            return array(
                'status' => 'error',
                'message' => 'File size must be less than 10MB.'
            );
        }

        // Create backup before uploading new logo
        $content_manager = new ContentManager();
        $content_manager->create_backup();

        // Handle upload
        if (!function_exists('wp_handle_upload')) {
            require_once(ABSPATH . 'wp-admin/includes/file.php');
        }

        $uploaded_file = wp_handle_upload($file, array('test_form' => false));

        if (isset($uploaded_file['error'])) {
            return array(
                'status' => 'error',
                'message' => 'Upload failed: ' . $uploaded_file['error']
            );
        }

        // Create attachment
        $attachment = array(
            'post_mime_type' => $uploaded_file['type'],
            'post_title' => 'Company Logo - ' . date('Y-m-d H:i:s'),
            'post_content' => '',
            'post_status' => 'inherit'
        );

        $attach_id = wp_insert_attachment($attachment, $uploaded_file['file']);

        if (is_wp_error($attach_id)) {
            return array(
                'status' => 'error',
                'message' => 'Failed to create attachment'
            );
        }

        // Generate attachment metadata
        if (!function_exists('wp_generate_attachment_metadata')) {
            require_once(ABSPATH . 'wp-admin/includes/image.php');
        }

        $attach_data = wp_generate_attachment_metadata($attach_id, $uploaded_file['file']);
        wp_update_attachment_metadata($attach_id, $attach_data);

        // Set as custom logo
        set_theme_mod('custom_logo', $attach_id);

        // Get image dimensions
        $image_info = getimagesize($uploaded_file['file']);

        return array(
            'status' => 'success',
            'message' => 'Logo uploaded successfully',
            'attachment_id' => $attach_id,
            'url' => $uploaded_file['url'],
            'width' => isset($image_info[0]) ? $image_info[0] : 0,
            'height' => isset($image_info[1]) ? $image_info[1] : 0,
            'file_size' => $file['size']
        );
    }

    /**
     * Get current logo information
     */
    private function get_current_logo() {
        $logo_id = get_theme_mod('custom_logo');

        if (!$logo_id) {
            return array(
                'has_logo' => false,
                'url' => '',
                'width' => 0,
                'height' => 0,
                'filename' => '',
                'file_size' => 0
            );
        }

        $logo_url = wp_get_attachment_image_url($logo_id, 'full');
        $logo_meta = wp_get_attachment_metadata($logo_id);
        $logo_post = get_post($logo_id);

        return array(
            'has_logo' => true,
            'attachment_id' => $logo_id,
            'url' => $logo_url,
            'width' => isset($logo_meta['width']) ? $logo_meta['width'] : 0,
            'height' => isset($logo_meta['height']) ? $logo_meta['height'] : 0,
            'filename' => basename($logo_url),
            'file_size' => filesize(get_attached_file($logo_id)) ?: 0
        );
    }

    public function activate() {
        try {
            // Create necessary database tables or options
            add_option('website_generator_version', '2.0.0');
            add_option('website_generator_backups', array());

            // Initialize default content and settings (safely)
            $this->initialize_default_content();

            // Initialize default design system (safely)
            $this->initialize_default_design_system();

            // Simple backup system uses WordPress options - no custom tables needed

            // Schedule initial backup creation for later (after all classes are loaded)
            add_action('init', array($this, 'create_initial_backup_delayed'), 20);

            error_log('Website Generator: Plugin activated successfully');

        } catch (Exception $e) {
            error_log('Website Generator: Activation error: ' . $e->getMessage());
            // Don't throw the error to prevent activation failure
        }
    }









    /**
     * Initialize default content and custom texts
     */
    private function initialize_default_content() {
        try {
            $default_custom_texts = array(
                'phone_number' => '(*************',
                'address' => '123 Main Street, Anytown, FL 12345',
                'email' => '<EMAIL>',
                'hero_title' => 'Premier Device Repair in Anytown, FL',
                'hero_subtitle' => 'Smartphones | Tablets | Computers | & More',
                'hero_description' => 'We understand when your device needs repair, you need it fixed fast and done right. Our certified technicians specialize in device repairs with genuine parts and warranty coverage.',
                'cta_title' => 'Ready to Get Your Device Fixed?',
                'cta_description' => 'We offer reliable and professional service for your electronic device by using the highest quality replacement parts currently available in the market.',
                'copyright_text' => '© 2023 All Rights Reserved | Not affiliated with Apple, Inc., LG Corp. or Samsung Corp',
                'benefit1_title' => 'Same Day Service',
                'benefit1_description' => 'Most repairs completed within hours, not days',
                'benefit2_title' => 'Expert Technicians',
                'benefit2_description' => 'Certified professionals with years of experience',
                'benefit3_title' => 'Quality Parts',
                'benefit3_description' => 'Only genuine and high-quality replacement parts',
                'benefit4_title' => 'Warranty Coverage',
                'benefit4_description' => 'All repairs backed by our comprehensive warranty'
            );

            // Only set if not already exists
            if (!get_option('website_generator_custom_texts')) {
                update_option('website_generator_custom_texts', $default_custom_texts);
            }

            error_log('Website Generator: Default content initialized');
        } catch (Exception $e) {
            error_log('Website Generator: Error initializing default content: ' . $e->getMessage());
        }
    }

    /**
     * Initialize default design system settings
     */
    private function initialize_default_design_system() {
        try {
            $defaults = array(
                'website_generator_primary_color' => '#165C9C',
                'website_generator_secondary_color' => '#6AA7E0',
                'website_generator_accent_color' => '#F7F7F7',
                'website_generator_button_style' => 'pill',
                'website_generator_button_size' => 'medium',
                'website_generator_button_text_color' => '#FFFFFF',
                'website_generator_typography_heading' => 'font-modern',
                'website_generator_typography_body' => 'font-modern',
                'website_generator_hero_animation' => 'fadeInUp',
                'website_generator_image_animation' => 'fadeIn',
                'website_generator_animation_speed' => 'normal',
                'website_generator_section_spacing' => 'normal'
            );

            foreach ($defaults as $option_name => $default_value) {
                // Only set if not already exists
                if (!get_option($option_name)) {
                    update_option($option_name, $default_value);
                }
            }

            // Set theme mods if not already set (only if theme supports it)
            if (function_exists('set_theme_mod')) {
                if (!get_theme_mod('primary_color')) {
                    set_theme_mod('primary_color', '#165C9C');
                }
                if (!get_theme_mod('secondary_color')) {
                    set_theme_mod('secondary_color', '#6AA7E0');
                }
            }

            error_log('Website Generator: Default design system initialized');
        } catch (Exception $e) {
            error_log('Website Generator: Error initializing default design system: ' . $e->getMessage());
        }
    }

    /**
     * Create initial backup after plugin activation (delayed)
     */
    public function create_initial_backup_delayed() {
        // Only run once after activation
        if (get_option('website_generator_initial_backup_created')) {
            return;
        }

        try {
            // Ensure ContentManager class is available
            if (!class_exists('ContentManager')) {
                error_log('Website Generator: ContentManager class not available for initial backup');
                return;
            }

            $content_manager = new ContentManager();
            $result = $content_manager->create_backup();

            if ($result['status'] === 'success') {
                error_log('Website Generator: Initial backup created successfully: ' . $result['backup_id']);
                update_option('website_generator_initial_backup_created', true);
            } else {
                error_log('Website Generator: Failed to create initial backup: ' . $result['message']);
            }
        } catch (Exception $e) {
            error_log('Website Generator: Exception creating initial backup: ' . $e->getMessage());
        }
    }

    /**
     * Handle restore default content AJAX request
     */
    public function handle_restore_default_content() {
        check_ajax_referer('website_generator_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        try {
            // Restore default content and design system
            $this->initialize_default_content();
            $this->initialize_default_design_system();

            wp_send_json_success(array(
                'message' => 'Default content and settings restored successfully'
            ));

        } catch (Exception $e) {
            wp_send_json_error('Failed to restore default content: ' . $e->getMessage());
        }
    }

    /**
     * Handle restore individual field to default AJAX request
     */
    public function handle_restore_field_default() {
        check_ajax_referer('website_generator_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        $field_name = sanitize_text_field(isset($_POST['field_name']) ? $_POST['field_name'] : '');
        $default_value = sanitize_textarea_field(isset($_POST['default_value']) ? $_POST['default_value'] : '');

        if (empty($field_name)) {
            wp_send_json_error('Field name is required');
        }

        try {
            // Get current custom texts
            $custom_texts = get_option('website_generator_custom_texts', array());

            // Map field names to custom text keys
            $field_mapping = array(
                'hero_heading' => 'hero_title',
                'hero_tagline' => 'hero_subtitle',
                'onestop_heading' => 'about_title',
                'onestop_description' => 'about_description',
                'buy_heading' => 'buy_title',
                'buy_description' => 'buy_description',
                'sell_heading' => 'sell_title',
                'sell_description' => 'sell_description',
                'cta_heading' => 'cta_title',
                'cta_description' => 'cta_description'
            );

            // Update the specific field
            if (isset($field_mapping[$field_name])) {
                $custom_texts[$field_mapping[$field_name]] = $default_value;
                update_option('website_generator_custom_texts', $custom_texts);
            }

            wp_send_json_success(array(
                'message' => 'Field restored to default successfully',
                'field_name' => $field_name,
                'default_value' => $default_value
            ));

        } catch (Exception $e) {
            wp_send_json_error('Failed to restore field default: ' . $e->getMessage());
        }
    }





    /**
     * Handle AJAX request to detect actual website colors
     */
    public function handle_detect_website_colors() {
        check_ajax_referer('website_generator_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        // Get the colors sent from the frontend JavaScript
        $detected_colors = array(
            'primary' => sanitize_hex_color($_POST['primary_color'] ?? '#165C9C'),
            'secondary' => sanitize_hex_color($_POST['secondary_color'] ?? '#6AA7E0'),
            'tertiary' => sanitize_hex_color($_POST['tertiary_color'] ?? '#F7F7F7'),
            'button_text' => sanitize_hex_color($_POST['button_text_color'] ?? '#FFFFFF')
        );

        // Store the detected colors for future use
        update_option('website_generator_detected_colors', $detected_colors);

        wp_send_json_success(array(
            'message' => 'Colors detected successfully',
            'colors' => $detected_colors
        ));
    }

    /**
     * Initialize backup system
     */
    private function init_backup_system() {
        // Hook into existing plugin actions for automatic backups
        add_action('website_generator_before_logo_upload', array($this, 'create_auto_backup'));
        add_action('website_generator_before_ai_generation', array($this, 'create_auto_backup'));
        add_action('website_generator_before_design_change', array($this, 'create_auto_backup'));

        // WordPress cron for scheduled backups (if enabled)
        if (get_option('website_generator_daily_backup', false)) {
            if (!wp_next_scheduled('website_generator_daily_backup')) {
                wp_schedule_event(time(), 'daily', 'website_generator_daily_backup');
            }
            add_action('website_generator_daily_backup', array($this, 'create_scheduled_backup'));
        }
    }

    /**
     * Create automatic backup before major operations
     */
    public function create_auto_backup($context = 'auto') {
        try {
            $content_manager = new ContentManager();
            $description = 'Auto Backup - ' . ucfirst($context) . ' (' . date('Y-m-d H:i:s') . ')';
            $result = $content_manager->create_backup($description);

            if ($result['status'] === 'success') {
                error_log('Website Generator: Auto backup created successfully - ' . $result['backup_id']);
            } else {
                error_log('Website Generator: Auto backup failed - ' . $result['message']);
            }

            return $result;

        } catch (Exception $e) {
            error_log('Website Generator: Auto backup exception - ' . $e->getMessage());
            return array(
                'status' => 'error',
                'message' => $e->getMessage()
            );
        }
    }

    /**
     * Create scheduled backup
     */
    public function create_scheduled_backup() {
        try {
            $content_manager = new ContentManager();
            $description = 'Scheduled Backup - ' . date('Y-m-d H:i:s');
            $result = $content_manager->create_backup($description);

            error_log('Website Generator: Scheduled backup completed - Status: ' . $result['status']);

        } catch (Exception $e) {
            error_log('Website Generator: Scheduled backup failed - ' . $e->getMessage());
        }
    }













    /**
     * Handle get business info AJAX request
     */
    public function handle_get_business_info() {
        check_ajax_referer('website_generator_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        try {
            $business_info_collector = new BusinessInfoCollector();
            $business_info_flat = $business_info_collector->get_business_info_for_ai();
            $completion_status = $business_info_collector->is_business_info_complete();

            wp_send_json_success($business_info_flat);

        } catch (Exception $e) {
            wp_send_json_error('Failed to load business information: ' . $e->getMessage());
        }
    }

    /**
     * Handle save business info AJAX request
     */
    public function handle_save_business_info() {
        check_ajax_referer('website_generator_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        // Extract business info from form data
        $business_info = array();
        $business_info_collector = new BusinessInfoCollector();
        $field_definitions = $business_info_collector->get_business_info_fields();

        // Get all field keys
        foreach ($field_definitions as $field_key => $field_config) {
            if (isset($_POST[$field_key])) {
                $value = $_POST[$field_key];

                // Handle array fields (like devices_repaired checkboxes)
                if (is_array($value)) {
                    $business_info[$field_key] = array_map('sanitize_text_field', $value);
                } else {
                    $business_info[$field_key] = sanitize_text_field($value);
                }
            }
        }

        if (empty($business_info)) {
            wp_send_json_error('No business information provided');
        }

        try {
            $result = $business_info_collector->update_business_info($business_info);

            // Get updated completion status
            $completion_status = $business_info_collector->is_business_info_complete();
            $result['completion_status'] = $completion_status;
            $result['completion_rate'] = $completion_status['completion_rate'];

            if ($result['status'] === 'success') {
                wp_send_json_success($result);
            } else {
                wp_send_json_error($result);
            }

        } catch (Exception $e) {
            wp_send_json_error('Failed to save business information: ' . $e->getMessage());
        }
    }

    /**
     * Handle preview AI content AJAX request
     */
    public function handle_preview_ai_content() {
        check_ajax_referer('website_generator_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        try {
            // Check if business info is saved first
            $business_info_collector = new BusinessInfoCollector();
            $completion_status = $business_info_collector->is_business_info_complete();

            if (!$completion_status['is_complete'] && $completion_status['completion_rate'] < 50) {
                wp_send_json_error('Please save your business information first (at least 50% complete) before previewing AI content.');
            }

            // Get current content for comparison
            $current_content = $this->get_current_content();
            $custom_texts = get_option('website_generator_custom_texts', array());

            // Generate AI content for preview
            $ai_generator = new AIContentGenerator();
            $business_info = $business_info_collector->get_business_info_for_ai();

            // Generate multiple content samples for preview
            $preview_samples = array();

            // Hero title
            $current_hero_title = $custom_texts['hero_heading'] ?? 'Premier Device Repair in Anytown, FL';
            $hero_title = $ai_generator->generate_content('hero_title', $current_hero_title, $business_info);
            if ($hero_title) {
                $preview_samples['hero_title'] = array(
                    'label' => 'Hero Title',
                    'current' => $current_hero_title,
                    'preview' => $hero_title
                );
            }

            // Hero description
            $current_hero_desc = $custom_texts['hero_description'] ?? 'We understand when your device needs repair, you need it fixed fast and done right.';
            $hero_description = $ai_generator->generate_content('hero_description', $current_hero_desc, $business_info);
            if ($hero_description) {
                $preview_samples['hero_description'] = array(
                    'label' => 'Hero Description',
                    'current' => $current_hero_desc,
                    'preview' => $hero_description
                );
            }

            // About title
            $current_about_title = $custom_texts['onestop_heading'] ?? 'Your One-Stop Shop Repair Store';
            $about_title = $ai_generator->generate_content('about_title', $current_about_title, $business_info);
            if ($about_title) {
                $preview_samples['about_title'] = array(
                    'label' => 'About Section Title',
                    'current' => $current_about_title,
                    'preview' => $about_title
                );
            }

            if (!empty($preview_samples)) {
                wp_send_json_success(array(
                    'message' => 'AI content preview generated successfully',
                    'preview_samples' => $preview_samples,
                    'business_info_used' => $business_info
                ));
            } else {
                wp_send_json_error('Failed to generate AI content preview');
            }

        } catch (Exception $e) {
            wp_send_json_error('Failed to generate AI content preview: ' . $e->getMessage());
        }
    }

    /**
     * Handle clear business info AJAX request
     */
    public function handle_clear_business_info() {
        check_ajax_referer('website_generator_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        try {
            $business_info_collector = new BusinessInfoCollector();
            $result = $business_info_collector->clear_all_business_info();

            if ($result['status'] === 'success') {
                wp_send_json_success(array(
                    'message' => 'All business information cleared successfully',
                    'cleared_count' => $result['cleared_count']
                ));
            } else {
                wp_send_json_error($result['message']);
            }

        } catch (Exception $e) {
            wp_send_json_error('Failed to clear business information: ' . $e->getMessage());
        }
    }



    /**
     * Handle apply section changes AJAX request
     */
    public function handle_apply_section_changes() {
        check_ajax_referer('website_generator_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        $section = sanitize_text_field($_POST['section'] ?? '');
        $section_data = $_POST['section_data'] ?? array();

        if (empty($section)) {
            wp_send_json_error('Section not specified');
        }

        try {
            // Create backup before applying changes
            $backup_manager = new BackupManager();
            $backup_result = $backup_manager->create_backup('section_' . $section . '_update');

            // Apply section-specific changes
            $result = $this->apply_section_data($section, $section_data);

            if ($result['success']) {
                wp_send_json_success(array(
                    'message' => ucfirst($section) . ' section updated successfully',
                    'section' => $section,
                    'backup_id' => $backup_result['backup_id'] ?? null
                ));
            } else {
                wp_send_json_error($result['message']);
            }

        } catch (Exception $e) {
            wp_send_json_error('Failed to apply section changes: ' . $e->getMessage());
        }
    }

    /**
     * Handle preview section changes AJAX request
     */
    public function handle_preview_section_changes() {
        check_ajax_referer('website_generator_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        $section = sanitize_text_field($_POST['section'] ?? '');
        $section_data = $_POST['section_data'] ?? array();

        if (empty($section)) {
            wp_send_json_error('Section not specified');
        }

        try {
            // Generate preview for section
            $preview_html = $this->generate_section_preview($section, $section_data);

            wp_send_json_success(array(
                'preview_html' => $preview_html,
                'section' => $section
            ));

        } catch (Exception $e) {
            wp_send_json_error('Failed to generate section preview: ' . $e->getMessage());
        }
    }

    /**
     * Apply data to a specific section
     */
    private function apply_section_data($section, $section_data) {
        $custom_texts = get_option('website_generator_custom_texts', array());
        $updated = false;

        switch ($section) {
            case 'hero':
                if (isset($section_data['hero_heading'])) {
                    $custom_texts['hero_heading'] = sanitize_text_field($section_data['hero_heading']);
                    $updated = true;
                }
                if (isset($section_data['hero_description'])) {
                    $custom_texts['hero_description'] = sanitize_textarea_field($section_data['hero_description']);
                    $updated = true;
                }
                break;

            case 'onestop':
                if (isset($section_data['onestop_heading'])) {
                    $custom_texts['onestop_heading'] = sanitize_text_field($section_data['onestop_heading']);
                    $updated = true;
                }
                if (isset($section_data['onestop_description'])) {
                    $custom_texts['onestop_description'] = sanitize_textarea_field($section_data['onestop_description']);
                    $updated = true;
                }
                break;

            case 'contact':
                if (isset($section_data['contact_phone'])) {
                    $custom_texts['contact_phone'] = sanitize_text_field($section_data['contact_phone']);
                    $updated = true;
                }
                if (isset($section_data['contact_email'])) {
                    $custom_texts['contact_email'] = sanitize_email($section_data['contact_email']);
                    $updated = true;
                }
                break;

            // Add more sections as needed
            default:
                return array('success' => false, 'message' => 'Unknown section: ' . $section);
        }

        if ($updated) {
            update_option('website_generator_custom_texts', $custom_texts);
            return array('success' => true, 'message' => 'Section updated successfully');
        } else {
            return array('success' => false, 'message' => 'No changes detected');
        }
    }

    /**
     * Generate preview HTML for a section
     */
    private function generate_section_preview($section, $section_data) {
        $preview_html = '<div class="section-preview">';

        switch ($section) {
            case 'hero':
                $preview_html .= '<h2>' . esc_html($section_data['hero_heading'] ?? 'Hero Heading') . '</h2>';
                $preview_html .= '<p>' . esc_html($section_data['hero_description'] ?? 'Hero description') . '</p>';
                break;

            case 'onestop':
                $preview_html .= '<h3>' . esc_html($section_data['onestop_heading'] ?? 'One-Stop Shop Heading') . '</h3>';
                $preview_html .= '<p>' . esc_html($section_data['onestop_description'] ?? 'One-stop shop description') . '</p>';
                break;

            case 'contact':
                $preview_html .= '<div class="contact-preview">';
                $preview_html .= '<p><strong>Phone:</strong> ' . esc_html($section_data['contact_phone'] ?? 'Phone number') . '</p>';
                $preview_html .= '<p><strong>Email:</strong> ' . esc_html($section_data['contact_email'] ?? 'Email address') . '</p>';
                $preview_html .= '</div>';
                break;

            default:
                $preview_html .= '<p>Preview for ' . esc_html($section) . ' section</p>';
        }

        $preview_html .= '</div>';
        return $preview_html;
    }

    /**
     * Handle save AI configuration AJAX request
     */
    public function handle_save_ai_config() {
        check_ajax_referer('website_generator_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        $config = $_POST['config'] ?? array();

        if (empty($config)) {
            wp_send_json_error('No configuration provided');
        }

        try {
            $claude_ai = new ClaudeAIManager();
            $result = $claude_ai->update_configuration($config);

            if ($result['success']) {
                wp_send_json_success($result);
            } else {
                // Extract the message from the result array for proper error display
                $error_message = isset($result['message']) ? $result['message'] : 'Configuration update failed';
                wp_send_json_error($error_message);
            }

        } catch (Exception $e) {
            wp_send_json_error('Failed to save AI configuration: ' . $e->getMessage());
        }
    }

    /**
     * Handle test AI connection AJAX request
     */
    public function handle_test_ai_connection() {
        check_ajax_referer('website_generator_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        // Get API key and model from the form (not stored config)
        $api_key = sanitize_text_field($_POST['claude_api_key'] ?? '');
        $model = sanitize_text_field($_POST['claude_model'] ?? '');

        if (empty($api_key)) {
            wp_send_json_error('API key is required for connection test');
        }

        try {
            $claude_ai = new ClaudeAIManager();

            // Test connection with provided credentials, not stored config
            $result = $claude_ai->test_connection_with_credentials($api_key, $model);

            if ($result['success']) {
                wp_send_json_success($result);
            } else {
                // Extract the error message from the result array for proper error display
                $error_message = isset($result['error']) ? $result['error'] : 'Connection test failed';
                wp_send_json_error($error_message);
            }

        } catch (Exception $e) {
            wp_send_json_error('Connection test failed: ' . $e->getMessage());
        }
    }

    /**
     * Handle clear AI configuration AJAX request
     */
    public function handle_clear_ai_config() {
        check_ajax_referer('website_generator_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        try {
            // Clear all Claude AI configuration options
            delete_option('website_generator_claude_api_key');
            delete_option('website_generator_claude_model');
            delete_option('website_generator_claude_max_tokens');
            delete_option('website_generator_claude_temperature');

            // Also clear usage statistics if desired
            $clear_stats = isset($_POST['clear_stats']) && $_POST['clear_stats'] === 'true';
            if ($clear_stats) {
                delete_option('website_generator_claude_hourly_usage');
                delete_option('website_generator_claude_daily_usage');
                delete_option('website_generator_claude_usage_log');
            }

            wp_send_json_success(array(
                'message' => 'AI configuration cleared successfully',
                'cleared_stats' => $clear_stats
            ));

        } catch (Exception $e) {
            wp_send_json_error('Failed to clear AI configuration: ' . $e->getMessage());
        }
    }

    /**
     * Handle save AI prompt AJAX request
     */
    public function handle_save_ai_prompt() {
        check_ajax_referer('website_generator_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        $ai_default_prompt = isset($_POST['ai_default_prompt']) ? wp_unslash($_POST['ai_default_prompt']) : '';

        if (empty($ai_default_prompt)) {
            wp_send_json_error('Prompt cannot be empty');
        }

        try {
            // Save the prompt
            update_option('website_generator_ai_default_prompt', $ai_default_prompt);

            wp_send_json_success(array(
                'message' => 'AI prompt saved successfully',
                'prompt_length' => strlen($ai_default_prompt)
            ));

        } catch (Exception $e) {
            wp_send_json_error('Failed to save AI prompt: ' . $e->getMessage());
        }
    }

    /**
     * Handle test AI prompt AJAX request
     */
    public function handle_test_ai_prompt() {
        check_ajax_referer('website_generator_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        $ai_default_prompt = isset($_POST['ai_default_prompt']) ? wp_unslash($_POST['ai_default_prompt']) : '';
        $test_content = isset($_POST['test_content']) ? sanitize_textarea_field($_POST['test_content']) : '';

        if (empty($ai_default_prompt)) {
            wp_send_json_error('Prompt cannot be empty');
        }

        if (empty($test_content)) {
            wp_send_json_error('Test content cannot be empty');
        }

        try {
            // Replace variables in the prompt
            $processed_prompt = str_replace('{ORIGINAL_CONTENT}', $test_content, $ai_default_prompt);

            // Add business info if available
            $business_name = get_option('website_generator_business_name', '');
            $business_phone = get_option('website_generator_business_phone', '');
            $business_email = get_option('website_generator_business_email', '');

            $processed_prompt = str_replace('{BUSINESS_NAME}', $business_name, $processed_prompt);
            $processed_prompt = str_replace('{BUSINESS_PHONE}', $business_phone, $processed_prompt);
            $processed_prompt = str_replace('{BUSINESS_EMAIL}', $business_email, $processed_prompt);

            // Use Claude AI to test the prompt
            $claude_ai = new ClaudeAIManager();
            $result = $claude_ai->generate_content($processed_prompt);

            if ($result['success']) {
                wp_send_json_success(array(
                    'message' => 'Prompt test successful',
                    'generated_content' => $result['content'],
                    'tokens_used' => isset($result['tokens_used']) ? $result['tokens_used'] : 'Unknown'
                ));
            } else {
                $error_message = isset($result['error']) ? $result['error'] : 'Prompt test failed';
                wp_send_json_error($error_message);
            }

        } catch (Exception $e) {
            wp_send_json_error('Failed to test AI prompt: ' . $e->getMessage());
        }
    }

    /**
     * Handle get AI usage stats AJAX request
     */
    public function handle_get_ai_usage_stats() {
        check_ajax_referer('website_generator_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        try {
            $claude_ai = new ClaudeAIManager();
            $stats = $claude_ai->get_usage_stats();

            wp_send_json_success($stats);

        } catch (Exception $e) {
            wp_send_json_error('Failed to get usage stats: ' . $e->getMessage());
        }
    }

    /**
     * Handle generate AI content AJAX request
     */
    public function handle_generate_ai_content() {
        check_ajax_referer('website_generator_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        $prompt_key = $_POST['prompt_key'] ?? '';
        $current_content = $_POST['current_content'] ?? '';
        $generate_variations = $_POST['generate_variations'] ?? false;
        $variation_count = intval($_POST['variation_count'] ?? 3);

        if (empty($prompt_key)) {
            wp_send_json_error('Prompt key is required');
        }

        // Add debug logging
        error_log('Website Generator AI Request: ' . print_r([
            'prompt_key' => $prompt_key,
            'content_length' => strlen($current_content),
            'generate_variations' => $generate_variations,
            'variation_count' => $variation_count
        ], true));

        try {
            // Define character-sensitive fields that should use specific field prompts
            $character_sensitive_fields = array('hero_heading', 'hero_tagline', 'about_title', 'about_description');

            // Check if we should use the custom prompt or traditional prompt system
            $use_custom_prompt = isset($_POST['use_custom_prompt']) ? $_POST['use_custom_prompt'] : true;

            // Override to use specific field prompts for character-sensitive fields
            if (in_array($prompt_key, $character_sensitive_fields)) {
                $use_custom_prompt = false; // Force use of specific field prompts
                error_log("Website Generator: Using specific field prompt for character-sensitive field: {$prompt_key}");
            }

            if ($use_custom_prompt) {
                // Use the custom default prompt system
                $default_prompt_text = 'You are an expert copywriter specializing in device repair marketing with deep understanding of customer psychology, local SEO, and conversion optimization for repair shops.

Your task is to transform the provided content into compelling, conversion-focused copy that addresses common customer concerns and drives action.

**KEY REPAIR SHOP CUSTOMER PAIN POINTS TO ADDRESS:**
1. Device urgency (broken phone = lost connection)
2. Trust concerns (will my data be safe?)
3. Cost anxiety (will it be worth fixing?)
4. Time sensitivity (how long without my device?)
5. Quality doubts (will it break again?)

**MARKETING PSYCHOLOGY PRINCIPLES:**
- Lead with empathy for device emergencies
- Emphasize quick turnaround times
- Highlight data security and privacy
- Use social proof and expertise signals
- Create urgency without being pushy
- Focus on value over price

**CONTENT TRANSFORMATION RULES:**
1. **CRITICAL: Respect character length** - Count characters as you write and stay within the original content length (±20%)
2. **Use different wording** while preserving core meaning and staying within character limits
3. **Include emotional triggers**: "emergency," "rescue," "restore," "save" (but keep concise)
4. **Add convenience factors**: "same-day," "while-you-wait," "free diagnostics" (space permitting)
5. **Incorporate trust signals**: "certified technicians," "warranty," "established since" (if space allows)
6. **Use local relevance**: neighborhood references, community focus (keep brief)
7. **Add urgency elements**: "quick," "immediate," "fast," "expedited" (use short words)
8. **Include outcome benefits**: "like new," "fully functional," "good as new" (concise phrasing)

**CHARACTER LENGTH GUIDELINES:**
- For headings (under 80 chars): Focus on 1-2 key benefits, keep location brief
- For taglines (under 90 chars): Use short device names, limit categories to fit
- For descriptions (under 300 chars): 2-3 sentences max, prioritize key benefits
- Always count characters including spaces and punctuation as you write

**DEVICE-SPECIFIC EXPERTISE TO SHOWCASE:**
- iPhone: Genuine parts, data recovery, water damage specialists
- Android: All brands supported, custom repairs, rooting issues
- Computers: Virus removal, hardware upgrades, data backup
- Gaming: Console modding, controller fixes, HDMI repairs
- Tablets: Screen calibration, charging ports, battery optimization

**CALL-TO-ACTION ENHANCEMENT:**
Transform generic CTAs into compelling action phrases:
- "Get Your Device Back Today"
- "Free Diagnosis - No Commitment"
- "Emergency Repair Available"
- "Save Your Device & Data"

**BUSINESS INFO INTEGRATION:**
When available, naturally incorporate:
- Business name for brand recognition
- Years in business for credibility
- Unique services for differentiation
- Location for local relevance
- Contact methods for accessibility

**Content to transform:**
{ORIGINAL_CONTENT}

**IMPORTANT:** Provide only the enhanced content that maintains the same purpose and structure while being more compelling and conversion-focused. No explanations or additional text.';

                $custom_prompt = get_option('website_generator_ai_default_prompt', $default_prompt_text);

                if (empty($custom_prompt)) {
                    // Set the default prompt if none exists
                    update_option('website_generator_ai_default_prompt', $default_prompt_text);
                    $custom_prompt = $default_prompt_text;
                }

                // Add character limit information for character-sensitive fields
                $character_limits = array(
                    'hero_heading' => 78,
                    'hero_tagline' => 86,
                    'about_title' => 64,
                    'about_description' => 300
                );

                if (isset($character_limits[$prompt_key])) {
                    $char_limit = $character_limits[$prompt_key];
                    $char_limit_instruction = "\n\n**CRITICAL CHARACTER LIMIT: MAXIMUM {$char_limit} CHARACTERS**\n";
                    $char_limit_instruction .= "Count every character as you write (letters, spaces, punctuation).\n";
                    $char_limit_instruction .= "STOP writing when you reach {$char_limit} characters.\n";
                    $char_limit_instruction .= "Current content length: " . strlen($current_content) . " characters.\n";
                    $char_limit_instruction .= "Generate content that is complete and under {$char_limit} characters total.\n";

                    $custom_prompt = $char_limit_instruction . $custom_prompt;
                }

                // Replace variables in the prompt
                $processed_prompt = str_replace('{ORIGINAL_CONTENT}', $current_content, $custom_prompt);

                // Add business info from BusinessInfoCollector if available and saved
                $business_info_collector = new BusinessInfoCollector();
                $completion_status = $business_info_collector->is_business_info_complete();

                // Only use business info if it's been saved (at least some completion)
                if ($completion_status['completion_rate'] > 0) {
                    $business_info = $business_info_collector->get_business_info_for_ai();

                    // Use comprehensive business info variable substitution
                    if (is_array($business_info) && !empty($business_info)) {
                        foreach ($business_info as $key => $value) {
                            // Only substitute if value is not empty
                            if (!empty($value)) {
                                $placeholder = '{' . strtoupper($key) . '}';
                                $processed_prompt = str_replace($placeholder, $value, $processed_prompt);

                                // Also support lowercase placeholders for backward compatibility
                                $placeholder_lower = '{' . $key . '}';
                                $processed_prompt = str_replace($placeholder_lower, $value, $processed_prompt);
                            }
                        }
                    }
                }
                
                // Remove any remaining placeholders
                $processed_prompt = preg_replace('/\{[^}]+\}/', '[not provided]', $processed_prompt);

                // Generate content using Claude AI directly
                $claude_ai = new ClaudeAIManager();

                // Check if Claude AI is configured
                if (!$claude_ai->is_configured()) {
                    wp_send_json_error('Claude AI is not configured. Please configure your API key in AI Settings first.');
                }

                if ($generate_variations) {
                    // Generate multiple variations
                    $variations = array();
                    for ($i = 0; $i < $variation_count; $i++) {
                        $result = $claude_ai->generate_content($processed_prompt);
                        if ($result['success']) {
                            $variations[] = array(
                                'content' => $result['content'],
                                'tokens_used' => isset($result['tokens_used']) ? $result['tokens_used'] : 0
                            );
                        }
                    }

                    if (!empty($variations)) {
                        $result = array(
                            'success' => true,
                            'variations' => $variations
                        );
                    } else {
                        $result = array(
                            'success' => false,
                            'error' => 'Failed to generate any variations'
                        );
                    }
                } else {
                    $result = $claude_ai->generate_content($processed_prompt);
                }

            } else {
                // Use traditional prompt system with specific field prompts
                $business_info_collector = new BusinessInfoCollector();
                $business_info = $business_info_collector->get_business_info_for_ai();

                // For character-sensitive fields, provide default business info if needed
                if (in_array($prompt_key, $character_sensitive_fields)) {
                    // Provide minimal default business info for character-sensitive fields
                    $default_business_info = array(
                        'business_name' => 'Your Business',
                        'city' => 'Anytown',
                        'state' => 'FL',
                        'devices_repaired' => 'Smartphones, Tablets, Computers',
                        'specializations' => 'Screen repair, battery replacement, data recovery',
                        'years_experience' => '5+',
                        'key_benefits' => 'Fast service, quality parts, warranty coverage',
                        'turnaround_time' => 'Same day',
                        'warranty_offered' => '90-day warranty'
                    );

                    // Merge with existing business info, using defaults for missing fields
                    $business_info = array_merge($default_business_info, $business_info);
                    error_log("Website Generator: Using enhanced business info for character-sensitive field: {$prompt_key}");
                } else {
                    // Check if business info is complete enough for other fields
                    $completion_status = $business_info_collector->is_business_info_complete();
                    if (!$completion_status['is_complete']) {
                        wp_send_json_error('Please complete your business information first (at least 80% required fields)');
                    }
                }

                // Generate content using specific field prompts
                $prompt_manager = new PromptManager();

                error_log("Website Generator: Using PromptManager for field: {$prompt_key}");

                if ($generate_variations) {
                    $result = $prompt_manager->generate_variations($prompt_key, $business_info, $current_content, $variation_count);
                } else {
                    $result = $prompt_manager->generate_content($prompt_key, $business_info, $current_content);
                }

                error_log("Website Generator: PromptManager result for {$prompt_key}: " . ($result['success'] ? 'SUCCESS' : 'FAILED'));
            }

            if ($result['success']) {
                // Add character length validation
                $validated_result = $this->validate_ai_content_length($result, $prompt_key);
                wp_send_json_success($validated_result);
            } else {
                // Extract error message from result for proper display
                $error_message = 'AI content generation failed';
                if (isset($result['error'])) {
                    $error_message = $result['error'];
                } elseif (isset($result['message'])) {
                    $error_message = $result['message'];
                }

                // Log the full error for debugging
                error_log('Website Generator AI Error: ' . print_r($result, true));

                wp_send_json_error($error_message);
            }

        } catch (Exception $e) {
            wp_send_json_error('AI content generation failed: ' . $e->getMessage());
        }
    }

    /**
     * Validate AI-generated content length based on field type
     */
    private function validate_ai_content_length($result, $prompt_key) {
        // Define character limits for different prompt types
        $character_limits = array(
            'hero_heading' => 78,    // 2x original 39 characters
            'hero_tagline' => 86,    // 2x original 43 characters
            'about_title' => 64,     // 2x original 32 characters
            'about_description' => 300  // Reasonable description length
        );

        // Get the character limit for this prompt type
        $max_length = isset($character_limits[$prompt_key]) ? $character_limits[$prompt_key] : null;

        if ($max_length === null) {
            // No length restriction for this field type
            return $result;
        }

        // Validate single content
        if (isset($result['content'])) {
            $content_length = strlen($result['content']);
            if ($content_length > $max_length) {
                // Log the issue but don't trim - Claude should have followed instructions
                error_log("Website Generator: WARNING - AI content exceeds limit for {$prompt_key}. Generated: {$content_length} chars, Max: {$max_length}. Content: " . substr($result['content'], 0, 100) . "...");
                $result['length_warning'] = "Generated content ({$content_length} chars) exceeds recommended limit of {$max_length} characters. Consider regenerating.";
            }
            $result['character_count'] = $content_length;
            $result['character_limit'] = $max_length;
        }

        // Validate variations if present
        if (isset($result['variations']) && is_array($result['variations'])) {
            foreach ($result['variations'] as $index => $variation) {
                if (isset($variation['content'])) {
                    $content_length = strlen($variation['content']);
                    if ($content_length > $max_length) {
                        // Log the issue but don't trim - Claude should have followed instructions
                        error_log("Website Generator: WARNING - AI variation {$index} exceeds limit for {$prompt_key}. Generated: {$content_length} chars, Max: {$max_length}");
                        $result['variations'][$index]['length_warning'] = "Content ({$content_length} chars) exceeds recommended limit of {$max_length} characters.";
                    }
                    $result['variations'][$index]['character_count'] = $content_length;
                    $result['variations'][$index]['character_limit'] = $max_length;
                }
            }
        }

        return $result;
    }

    /**
     * Handle get prompt templates AJAX request
     */
    public function handle_get_prompt_templates() {
        check_ajax_referer('website_generator_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        try {
            $prompt_manager = new PromptManager();
            $prompts = $prompt_manager->get_prompts_by_type(true); // Active only
            $content_types = $prompt_manager->get_content_types();

            wp_send_json_success(array(
                'prompts' => $prompts,
                'content_types' => $content_types
            ));

        } catch (Exception $e) {
            wp_send_json_error('Failed to get prompt templates: ' . $e->getMessage());
        }
    }

    /**
     * Handle get AI generation history AJAX request
     */
    public function handle_get_ai_generation_history() {
        check_ajax_referer('website_generator_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        try {
            global $wpdb;
            $table_name = $wpdb->prefix . 'website_generator_ai_history';

            $limit = intval($_POST['limit'] ?? 50);
            $offset = intval($_POST['offset'] ?? 0);

            // Get total count
            $total_count = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");

            // Get history records
            $history = $wpdb->get_results(
                $wpdb->prepare(
                    "SELECT * FROM $table_name ORDER BY created_at DESC LIMIT %d OFFSET %d",
                    $limit,
                    $offset
                ),
                ARRAY_A
            );

            // Process history data
            foreach ($history as &$record) {
                $record['business_info_snapshot'] = json_decode($record['business_info_snapshot'], true);
                $record['applied'] = (bool) $record['applied'];
                $record['created_at_formatted'] = date('M j, Y g:i A', strtotime($record['created_at']));

                // Truncate long content for display
                if (strlen($record['generated_content']) > 100) {
                    $record['generated_content_preview'] = substr($record['generated_content'], 0, 100) . '...';
                } else {
                    $record['generated_content_preview'] = $record['generated_content'];
                }
            }

            wp_send_json_success(array(
                'history' => $history,
                'total_count' => $total_count,
                'has_more' => ($offset + $limit) < $total_count
            ));

        } catch (Exception $e) {
            wp_send_json_error('Failed to get AI generation history: ' . $e->getMessage());
        }
    }

    /**
     * Handle clear AI generation history AJAX request
     */
    public function handle_clear_ai_generation_history() {
        check_ajax_referer('website_generator_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        try {
            global $wpdb;
            $table_name = $wpdb->prefix . 'website_generator_ai_history';

            $days_to_keep = intval($_POST['days_to_keep'] ?? 0);

            if ($days_to_keep > 0) {
                // Clear history older than specified days
                $cutoff_date = date('Y-m-d H:i:s', strtotime("-$days_to_keep days"));
                $deleted = $wpdb->query(
                    $wpdb->prepare("DELETE FROM $table_name WHERE created_at < %s", $cutoff_date)
                );
                $message = "Cleared $deleted history records older than $days_to_keep days";
            } else {
                // Clear all history
                $deleted = $wpdb->query("DELETE FROM $table_name");
                $message = "Cleared all $deleted history records";
            }

            wp_send_json_success(array(
                'deleted_count' => $deleted,
                'message' => $message
            ));

        } catch (Exception $e) {
            wp_send_json_error('Failed to clear AI generation history: ' . $e->getMessage());
        }
    }

    /**
     * Handle save business questionnaire AJAX request
     */
    public function handle_save_business_questionnaire() {
        check_ajax_referer('website_generator_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        try {
            $questionnaire_data = $_POST['questionnaire_data'] ?? array();

            if (empty($questionnaire_data)) {
                wp_send_json_error('No questionnaire data provided');
            }

            // Validate required fields
            $required_fields = array('business_name', 'city', 'state', 'phone', 'address');
            foreach ($required_fields as $field) {
                if (empty($questionnaire_data[$field])) {
                    wp_send_json_error("Required field missing: $field");
                }
            }

            // Validate services
            if (empty($questionnaire_data['services']) || !is_array($questionnaire_data['services'])) {
                wp_send_json_error('At least one service must be selected');
            }

            // Validate characteristics
            if (empty($questionnaire_data['characteristics']) || !is_array($questionnaire_data['characteristics'])) {
                wp_send_json_error('At least one business characteristic must be selected');
            }

            // Save to business info collector
            if (class_exists('BusinessInfoCollector')) {
                $collector = new BusinessInfoCollector();

                // Map questionnaire data to business info format
                $business_info = array(
                    'business_name' => sanitize_text_field($questionnaire_data['business_name']),
                    'city' => sanitize_text_field($questionnaire_data['city']),
                    'state' => sanitize_text_field($questionnaire_data['state']),
                    'phone' => sanitize_text_field($questionnaire_data['phone']),
                    'email' => sanitize_email($questionnaire_data['email'] ?? ''),
                    'address' => sanitize_text_field($questionnaire_data['address']),
                    'business_hours' => sanitize_text_field($questionnaire_data['business_hours'] ?? ''),
                    'years_in_business' => sanitize_text_field($questionnaire_data['years_in_business'] ?? ''),
                    'team_size' => sanitize_text_field($questionnaire_data['team_size'] ?? ''),
                    'special_certifications' => sanitize_textarea_field($questionnaire_data['special_certifications'] ?? ''),
                    'facebook_url' => esc_url_raw($questionnaire_data['facebook_url'] ?? ''),
                    'instagram_url' => esc_url_raw($questionnaire_data['instagram_url'] ?? ''),
                    'google_business_url' => esc_url_raw($questionnaire_data['google_business_url'] ?? ''),
                    'footer_copyright' => sanitize_text_field($questionnaire_data['footer_copyright'] ?? ''),
                    'special_notes' => sanitize_textarea_field($questionnaire_data['special_notes'] ?? ''),

                    // Convert arrays to comma-separated strings for storage
                    'services' => implode(',', array_map('sanitize_text_field', $questionnaire_data['services'])),
                    'characteristics' => implode(',', array_map('sanitize_text_field', $questionnaire_data['characteristics'])),
                    'additional_services' => implode(',', array_map('sanitize_text_field', $questionnaire_data['additional_services'] ?? array()))
                );

                // Save the business information
                $result = $collector->save_business_info($business_info);

                if ($result) {
                    wp_send_json_success(array(
                        'message' => 'Business questionnaire saved successfully',
                        'completion_percentage' => $collector->get_completion_percentage()
                    ));
                } else {
                    wp_send_json_error('Failed to save business information');
                }
            } else {
                wp_send_json_error('BusinessInfoCollector class not found');
            }

        } catch (Exception $e) {
            wp_send_json_error('Failed to save business questionnaire: ' . $e->getMessage());
        }
    }

    /**
     * Handle save Airtable configuration AJAX request
     */
    public function handle_save_airtable_config() {
        check_ajax_referer('airtable_config_nonce', 'airtable_config_nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        try {
            $enabled = isset($_POST['airtable_integration_enabled']) ? true : false;
            $api_key = sanitize_text_field($_POST['airtable_api_key'] ?? '');
            $webhook_secret = sanitize_text_field($_POST['airtable_webhook_secret'] ?? '');
            $site_identifier = sanitize_text_field($_POST['airtable_site_identifier'] ?? '');
            $rate_limit = intval($_POST['airtable_rate_limit'] ?? 60);

            // Validate site identifier format
            if (!empty($site_identifier) && !preg_match('/^[a-z0-9-]+$/', $site_identifier)) {
                wp_send_json_error('Site identifier must contain only lowercase letters, numbers, and hyphens');
            }

            // Save configuration
            update_option('airtable_integration_enabled', $enabled);
            update_option('airtable_api_key', $api_key);
            update_option('airtable_webhook_secret', $webhook_secret);
            update_option('airtable_site_identifier', $site_identifier);
            update_option('airtable_rate_limit', $rate_limit);

            wp_send_json_success(array(
                'message' => 'Airtable configuration saved successfully',
                'webhook_url' => admin_url('admin-ajax.php?action=airtable_webhook')
            ));

        } catch (Exception $e) {
            wp_send_json_error('Failed to save Airtable configuration: ' . $e->getMessage());
        }
    }

    /**
     * Handle test Airtable webhook AJAX request
     */
    public function handle_test_airtable_webhook() {
        check_ajax_referer('website_generator_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        try {
            // Check if integration is enabled
            if (!get_option('airtable_integration_enabled', false)) {
                wp_send_json_error('Airtable integration is not enabled');
            }

            // Check if API key is configured
            $api_key = get_option('airtable_api_key', '');
            if (empty($api_key)) {
                wp_send_json_error('API key is not configured');
            }

            // Create test webhook data
            $test_data = array(
                'api_key' => $api_key,
                'site_identifier' => get_option('airtable_site_identifier', 'test-site'),
                'business_name' => 'Test Repair Shop',
                'city' => 'Test City',
                'state' => 'FL',
                'hero_heading' => 'Test Hero Heading',
                'test_mode' => true
            );

            // Send test webhook to ourselves
            $webhook_url = admin_url('admin-ajax.php?action=airtable_webhook');
            $response = wp_remote_post($webhook_url, array(
                'body' => json_encode($test_data),
                'headers' => array(
                    'Content-Type' => 'application/json',
                    'X-API-Key' => $api_key
                ),
                'timeout' => 30
            ));

            if (is_wp_error($response)) {
                wp_send_json_error('Webhook test failed: ' . $response->get_error_message());
            }

            $response_code = wp_remote_retrieve_response_code($response);
            $response_body = wp_remote_retrieve_body($response);

            if ($response_code === 200) {
                wp_send_json_success(array(
                    'message' => 'Webhook test successful',
                    'response_code' => $response_code,
                    'response_body' => $response_body
                ));
            } else {
                wp_send_json_error('Webhook test failed with response code: ' . $response_code . '. Response: ' . $response_body);
            }

        } catch (Exception $e) {
            wp_send_json_error('Webhook test failed: ' . $e->getMessage());
        }
    }

    /**
     * Handle get Airtable logs AJAX request
     */
    public function handle_get_airtable_logs() {
        check_ajax_referer('website_generator_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        try {
            $logs = get_option('airtable_webhook_logs', array());

            // Reverse to show newest first
            $logs = array_reverse($logs);

            // Limit to last 50 entries for display
            $logs = array_slice($logs, 0, 50);

            wp_send_json_success(array(
                'logs' => $logs,
                'count' => count($logs)
            ));

        } catch (Exception $e) {
            wp_send_json_error('Failed to get Airtable logs: ' . $e->getMessage());
        }
    }

    /**
     * Handle clear Airtable logs AJAX request
     */
    public function handle_clear_airtable_logs() {
        check_ajax_referer('website_generator_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        try {
            delete_option('airtable_webhook_logs');

            wp_send_json_success(array(
                'message' => 'Airtable logs cleared successfully'
            ));

        } catch (Exception $e) {
            wp_send_json_error('Failed to clear Airtable logs: ' . $e->getMessage());
        }
    }

    /**
     * Handle run Airtable test suite AJAX request
     */
    public function handle_run_airtable_test_suite() {
        check_ajax_referer('website_generator_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        try {
            $testing_tools = new AirtableTestingTools();
            $test_results = $testing_tools->run_test_suite();

            wp_send_json_success($test_results);

        } catch (Exception $e) {
            wp_send_json_error('Failed to run test suite: ' . $e->getMessage());
        }
    }

    /**
     * Handle check Airtable database data AJAX request
     */
    public function handle_check_airtable_database_data() {
        check_ajax_referer('website_generator_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        try {
            // Get all relevant database values
            $custom_texts = get_option('website_generator_custom_texts', array());
            $business_info = get_option('website_generator_business_info', array());
            $design_settings = get_option('website_generator_design_settings', array());
            $webhook_logs = get_option('airtable_webhook_logs', array());

            // Get only the last 3 webhook logs for display
            $recent_logs = array_slice(array_reverse($webhook_logs), 0, 3);

            // ALSO GET HOMEPAGE CONTENT TO UNDERSTAND THE STRUCTURE
            $homepage_id = get_option('page_on_front');
            $homepage_content = '';
            $homepage_title = '';
            if ($homepage_id) {
                $homepage = get_post($homepage_id);
                if ($homepage) {
                    $homepage_content = $homepage->post_content;
                    $homepage_title = $homepage->post_title;
                }
            }

            wp_send_json_success(array(
                'custom_texts' => $custom_texts,
                'business_info' => $business_info,
                'design_settings' => $design_settings,
                'webhook_logs' => $recent_logs,
                'custom_texts_count' => count($custom_texts),
                'business_info_count' => count($business_info),
                'design_settings_count' => count($design_settings),
                'webhook_logs_count' => count($webhook_logs),
                'hero_heading_value' => $custom_texts['hero_heading'] ?? 'NOT_SET',
                'homepage_id' => $homepage_id,
                'homepage_title' => $homepage_title,
                'homepage_content' => $homepage_content,
                'homepage_content_length' => strlen($homepage_content),
                'homepage_content_preview' => substr($homepage_content, 0, 2000) // Show first 2000 chars for analysis
            ));

        } catch (Exception $e) {
            wp_send_json_error('Failed to check database data: ' . $e->getMessage());
        }
    }

    /**
     * Test the ContentBlockManager
     */
    public function handle_test_content_block_manager() {
        check_ajax_referer('website_generator_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        try {
            require_once plugin_dir_path(__FILE__) . 'includes/content-block-manager.php';
            $block_manager = new ContentBlockManager();

            $test_heading = $_POST['test_heading'] ?? 'Test Hero Heading from Block Manager';

            // Test the update
            $result = $block_manager->update_hero_heading($test_heading);

            // Also get structure analysis
            $structure = $block_manager->analyze_homepage_structure();

            wp_send_json_success(array(
                'update_result' => $result,
                'structure_analysis' => $structure,
                'test_heading' => $test_heading
            ));

        } catch (Exception $e) {
            wp_send_json_error('Failed to test content block manager: ' . $e->getMessage());
        }
    }

}



// Initialize the plugin
new WebsiteGeneratorPro();
