<?php
/**
 * Test Concatenated Record Parser
 * 
 * This script tests the emergency parser that handles malformed Airtable data
 * where all fields are concatenated into a single "record" field.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    define('ABSPATH', dirname(__FILE__) . '/');
}

/**
 * Test the concatenated record parser
 */
function test_concatenated_parser() {
    echo "<h2>🧪 Concatenated Record Parser Test</h2>\n";
    
    require_once 'includes/airtable-integration.php';
    
    // Create a test instance (we'll use reflection to access private method)
    $integration = new AirtableIntegration();
    
    // Test data that mimics the malformed Airtable automation output
    $test_cases = array(
        array(
            'name' => 'Real Example from Log',
            'input' => array(
                'api_key' => 'test-key',
                'record' => 'Premium Phone Repair Shop in Medellinexample-siteSmartphones | Tablets | Computers | & Consoles'
            ),
            'expected' => array(
                'site_identifier' => 'example-site',
                'hero_title' => 'Premium Phone Repair Shop in Medellin',
                'hero_tagline' => 'Smartphones | Tablets | Computers | & Consoles'
            )
        ),
        array(
            'name' => 'Another Site Example',
            'input' => array(
                'api_key' => 'test-key',
                'record' => 'Fast Phone Repair Servicemiami-siteDevices | Electronics | & More'
            ),
            'expected' => array(
                'site_identifier' => 'miami-site',
                'hero_title' => 'Fast Phone Repair Service',
                'hero_tagline' => 'Devices | Electronics | & More'
            )
        ),
        array(
            'name' => 'Test Site Example',
            'input' => array(
                'api_key' => 'test-key',
                'record' => 'Professional Repair Shoptest-siteSmartphones | Tablets | Computers'
            ),
            'expected' => array(
                'site_identifier' => 'test-site',
                'hero_title' => 'Professional Repair Shop',
                'hero_tagline' => 'Smartphones | Tablets | Computers'
            )
        )
    );
    
    // Use reflection to access the private method
    $reflection = new ReflectionClass($integration);
    $method = $reflection->getMethod('parse_concatenated_record');
    $method->setAccessible(true);
    
    foreach ($test_cases as $i => $test_case) {
        echo "<h3>Test " . ($i + 1) . ": " . $test_case['name'] . "</h3>\n";
        echo "<strong>Input:</strong> " . $test_case['input']['record'] . "\n";
        
        $result = $method->invoke($integration, $test_case['input']);
        
        echo "<strong>Parsed Results:</strong>\n";
        echo "- Site Identifier: " . ($result['site_identifier'] ?? 'NOT_FOUND') . "\n";
        echo "- Hero Title: " . ($result['Hero Heading'] ?? 'NOT_FOUND') . "\n";
        echo "- Hero Tagline: " . ($result['Hero Tagline'] ?? 'NOT_FOUND') . "\n";
        
        // Check if parsing was successful
        $success = true;
        $errors = array();
        
        if (!isset($result['site_identifier']) || $result['site_identifier'] !== $test_case['expected']['site_identifier']) {
            $success = false;
            $errors[] = "Site identifier mismatch";
        }
        
        if (!isset($result['Hero Heading']) || $result['Hero Heading'] !== $test_case['expected']['hero_title']) {
            $success = false;
            $errors[] = "Hero title mismatch";
        }
        
        if (!isset($result['Hero Tagline']) || $result['Hero Tagline'] !== $test_case['expected']['hero_tagline']) {
            $success = false;
            $errors[] = "Hero tagline mismatch";
        }
        
        if ($success) {
            echo "✅ <strong>PASS</strong> - All fields parsed correctly\n";
        } else {
            echo "❌ <strong>FAIL</strong> - " . implode(', ', $errors) . "\n";
            echo "<strong>Expected:</strong>\n";
            echo "- Site Identifier: " . $test_case['expected']['site_identifier'] . "\n";
            echo "- Hero Title: " . $test_case['expected']['hero_title'] . "\n";
            echo "- Hero Tagline: " . $test_case['expected']['hero_tagline'] . "\n";
        }
        
        echo "\n";
    }
    
    echo "<h3>✅ Parser Test Complete</h3>\n";
    echo "<p>The emergency parser should now handle malformed Airtable automation data.</p>\n";
}

// Run the test if this file is accessed directly
if (basename($_SERVER['PHP_SELF']) == basename(__FILE__)) {
    test_concatenated_parser();
}
?>
